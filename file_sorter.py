from pathlib import Path
import shutil
import docx
from docx.shared import Cm, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
import PyPDF2
import textract
import hashlib
import os

# 获取脚本所在目录作为工作目录
work_dir = Path(__file__).parent

# 分类规则配置 - 在此处添加/修改分类和关键词
# 格式: {'name': '分类名称', 'keywords': ['关键词1', '关键词2'], 'match_type': 'contains'/'startswith'/'default', 'priority': 整数}
# - contains: 文件名包含任一关键词即匹配
# - startswith: 文件名以任一关键词开头即匹配
# - default: 默认分类，当其他分类都不匹配时使用（仅设置一个）
# - priority: 优先级数字(越高越优先)，用于解决多规则匹配冲突
CATEGORIES_CONFIG = [
    {
        'name': '美盟方向',
        'keywords': ['美','日','韩','澳','拜登','特朗普'],
        'match_type': 'contains',
        'priority': 1  # 较低优先级
    },
    {
        'name': '中东方向',
        'keywords': ['伊朗', '巴以','胡赛','叙','伊拉克',''],
        'match_type': 'contains',
        'priority': 1  # 较高优先级
    },
    {
        'name': '台湾方向',
        'keywords': ['台', '对台','美台','台湾'],
        'match_type': 'contains',
        'priority': 1  # 较高优先级
    },
     {
        'name': '半岛方向',
        'keywords': ['朝鲜','朝','韩国'],
        'match_type': 'contains',
        'priority': 1  # 较高优先级
    },
    {
        'name': '俄乌方向',
        'keywords': ['俄','俄乌','俄罗斯','乌克兰'],
        'match_type': 'contains',
        'priority': 1  # 较高优先级
    },
        {
        'name': '南亚方向',
        'keywords': ['缅','越','柬','缅甸','越南','柬埔寨'],
        'match_type': 'contains',
        'priority': 1  # 较高优先级
    }, 
    {
        'name': '反恐维稳',
        'keywords': ['维和','恐怖','维稳'],
        'match_type': 'contains',
        'priority': 1  # 较高优先级
    },         
    {
        'name': '涉我方向',
        'keywords': [],
        'match_type': 'default',
        'priority': 1
    }
]

# 根据配置生成分类判断函数
def get_category(name):
    matched_categories = []
    default_category = None
    
    for config in CATEGORIES_CONFIG:
        # 检查是否匹配当前分类规则
        if config['match_type'] == 'contains' and any(keyword in name for keyword in config['keywords']):
            matched_categories.append(config)
        elif config['match_type'] == 'startswith' and any(name.startswith(keyword) for keyword in config['keywords']):
            matched_categories.append(config)
        elif config['match_type'] == 'default':
            default_category = config['name']
    
    if matched_categories:
        # 按优先级排序，取最高优先级的分类
        matched_categories.sort(key=lambda x: x.get('priority', 0), reverse=True)
        return matched_categories[0]['name']
    
    return default_category
# 创建分类文件夹（如果不存在）
for config in CATEGORIES_CONFIG:
    (work_dir / config['name']).mkdir(exist_ok=True)

# 文档汇总功能 - 初始化汇总文档
def init_summary_doc():
    doc = docx.Document()
    # 设置页边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Cm(3.7)
        section.bottom_margin = Cm(3.5)
        section.left_margin = Cm(2.8)
        section.right_margin = Cm(2.6)
    return doc

# 添加内容到汇总文档
def add_content_to_summary(doc, title, content):
    # 添加标题
    title_para = doc.add_paragraph(title)
    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = title_para.runs[0]
    title_run.font.name = '方正小标宋简体'
    title_run._element.rPr.rFonts.set(qn('w:eastAsia'), '方正小标宋简体')
    title_run.font.size = Pt(22)  # 二号字对应22pt
    title_run.font.bold = True
    doc.add_paragraph()  # 添加空行

    # 添加正文
    content_para = doc.add_paragraph()
    content_run = content_para.add_run(content)
    content_run.font.name = '仿宋GB2312'
    content_run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋GB2312')
    content_run.font.size = Pt(16)  # 三号字对应16pt
    # 设置首行缩进2个字符
    content_para.paragraph_format.first_line_indent = Pt(32)  # 2个中文字符约32pt
    # 设置行距28.95磅
    content_para.paragraph_format.line_spacing = Pt(28.95)
    doc.add_page_break()  # 分页

# 添加目录到文档
def add_table_of_contents(doc):
    """添加目录并返回目录段落"""
    para = doc.add_paragraph()
    run = para.add_run()
    
    # 添加目录字段代码
    fld_char_begin = OxmlElement('w:fldChar')
    fld_char_begin.set(qn('w:fldCharType'), 'begin')
    run._r.append(fld_char_begin)
    
    instr_text = OxmlElement('w:instrText')
    instr_text.text = r'TOC \o "1-2" \h \z \u'  # 显示1-2级标题，带超链接
    run._r.append(instr_text)
    
    fld_char_end = OxmlElement('w:fldChar')
    fld_char_end.set(qn('w:fldCharType'), 'end')
    run._r.append(fld_char_end)
    
    return para  # 返回目录段落

# 添加标题到汇总文档
def add_title_to_summary(doc, title, level):
    """添加指定级别的标题到汇总文档"""
    title_para = doc.add_paragraph(title, style=f'Heading {level}')
    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    title_run = title_para.runs[0]
    title_run.font.name = '方正小标宋简体'
    title_run._element.rPr.rFonts.set(qn('w:eastAsia'), '方正小标宋简体')
    
    if level == 1:
        title_run.font.size = Pt(22)  # 一级标题字号
    else:
        title_run.font.size = Pt(16)  # 二级标题字号
        
    title_run.font.bold = True
    doc.add_paragraph()  # 添加空行

# 修改原add_content_to_summary函数仅保留内容添加功能
def add_content_to_summary(doc, content):
    """添加正文内容到汇总文档"""
    content_para = doc.add_paragraph()
    content_run = content_para.add_run(content)
    content_run.font.name = '仿宋GB2312'
    content_run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋GB2312')
    content_run.font.size = Pt(16)  # 三号字对应16pt
    # 设置首行缩进2个字符
    content_para.paragraph_format.first_line_indent = Pt(32)
    # 设置行距28.95磅
    content_para.paragraph_format.line_spacing = Pt(28.95)
    doc.add_page_break()  # 分页

# 提取不同类型文件内容
def extract_file_content(file_path):
    file_ext = file_path.suffix.lower()
    content = ''
    try:
        if file_ext == '.txt':
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        elif file_ext == '.docx':
            doc = docx.Document(file_path)
            content = '\n'.join([para.text for para in doc.paragraphs])
        elif file_ext == '.doc':
            # 需要安装antiword: https://www.softpedia.com/get/Office-tools/Other-Office-Tools/Antiword.shtml
            content = textract.process(str(file_path)).decode('utf-8', errors='ignore')
        elif file_ext == '.pdf':
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                content = '\n'.join([page.extract_text() for page in reader.pages])
    except Exception as e:
        print(f"提取文件 {file_path.name} 内容失败: {e}")
    # 添加文本标准化处理
    if content:
        # 去除多余空白和标准化换行符
        content = ' '.join(content.split())
    # 添加title定义，使用文件名作为标题
    title = file_path.stem  # 获取不带扩展名的文件名作为标题
    return title, content

# 初始化汇总文档
summary_doc = init_summary_doc()

# 初始化已处理标题集合和分类文件映射
processed_titles = set()
category_files = {}

# 遍历文件并分类收集内容
for item in work_dir.iterdir():
    # 跳过文件夹、临时文件、脚本本身和汇总文档
    if item.is_file() and not item.name.startswith('~$') and item.name != 'file_sorter.py' and item.name != '文章汇总.docx' and item.suffix.lower() in ['.docx', '.doc', '.pdf', '.txt']:
        # 提取文件内容
        title, content = extract_file_content(item)
        
        # 基于标题去重检查
        if title in processed_titles or not (isinstance(content, str) and title and content.strip()):
            print(f"跳过无效或重复文件: {item.name}")
            continue
        
        # 获取分类
        category = get_category(item.name)
        if not category:
            print(f"无法确定分类，跳过文件: {item.name}")
            continue
        
        # 添加到分类集合
        if category not in category_files:
            category_files[category] = []
        category_files[category].append((title, content))
        processed_titles.add(title)

# 添加目录
add_table_of_contents(summary_doc)

# 按分类优先级排序并添加内容
category_priority = {config['name']: config.get('priority', 0) for config in CATEGORIES_CONFIG}
for category in sorted(category_files.keys(), key=lambda x: category_priority.get(x, 0), reverse=True):
    add_title_to_summary(summary_doc, category, level=1)
    for title, content in category_files[category]:
        add_title_to_summary(summary_doc, title, level=2)
        add_content_to_summary(summary_doc, content)

# ======== 所有内容添加完成后，最后添加目录 ========
# 添加目录
toc_para = add_table_of_contents(summary_doc)
# 添加目录后的分页符
summary_doc.add_page_break()

# 将目录移动到文档开头
summary_doc._body._element.insert(0, toc_para._element)  # 使用_element属性获取lxml元素
    # 移除原位置的空段落（添加目录时创建的）
for para in summary_doc.paragraphs:
        if not para.text.strip():
            p = para._element
            p.getparent().remove(p)
            break

# 文件分类移动逻辑（保持不变）
for item in work_dir.iterdir():
    if item.is_file() and not item.name.startswith('~$') and item.name != 'file_sorter.py' and item.name != '文章汇总.docx' and item.suffix.lower() in ['.docx', '.doc', '.pdf', '.txt']:
        category = get_category(item.name)
        if category:
            dest_path = work_dir / category / item.name
            try:
                if dest_path.exists():
                    # 如果目标文件已存在，添加数字后缀避免覆盖
                    counter = 1
                    while True:
                        new_name = f"{item.stem}_{counter}{item.suffix}"
                        dest_path = work_dir / category / new_name
                        if not dest_path.exists():
                            break
                        counter += 1
                shutil.move(str(item), str(dest_path))
                print(f"已移动: {item.name} -> {category}/{dest_path.name}")
            except Exception as e:
                print(f"移动文件 {item.name} 时出错: {e}")

# 保存汇总文档
summary_path = work_dir / '文章汇总.docx'
summary_doc.save(summary_path)
print(f"汇总文档已生成: {summary_path}")
print("提示: 打开文档后需右键点击目录选择'更新域'以刷新目录内容和页码")
"""
现代化UI主题和样式配置
提供统一的界面风格、色彩搭配和控件样式
"""

import os
import tkinter as tk
from tkinter import ttk


class ModernTheme:
    """现代化UI主题配置"""
    
    # 主色调配置
    COLORS = {
        # 主色调 - 优化对比度和专业性
        'primary': '#0EA5E9',      # 专业蓝色 - 更好的可读性
        'primary_light': '#38BDF8', # 浅蓝色
        'primary_dark': '#0284C7',  # 深蓝色
        'primary_hover': '#0369A1', # 悬停蓝色

        # 辅助色 - 和谐配色方案
        'secondary': '#64748B',    # 中性灰蓝
        'secondary_light': '#94A3B8', # 浅灰蓝
        'secondary_dark': '#475569',  # 深灰蓝

        'accent': '#8B5CF6',       # 紫色强调
        'accent_light': '#A78BFA', # 浅紫色
        'accent_dark': '#7C3AED',  # 深紫色

        # 功能色 - 清晰的语义区分
        'success': '#10B981',      # 成功绿色
        'success_light': '#34D399', # 浅绿色
        'success_dark': '#059669',  # 深绿色

        'warning': '#F59E0B',      # 警告橙色
        'warning_light': '#FBBF24', # 浅橙色
        'warning_dark': '#D97706',  # 深橙色

        'danger': '#EF4444',       # 错误红色
        'danger_light': '#F87171', # 浅红色
        'danger_dark': '#DC2626',  # 深红色

        'info': '#3B82F6',         # 信息蓝色
        'info_light': '#60A5FA',   # 浅信息蓝
        'info_dark': '#2563EB',    # 深信息蓝

        # 背景色系 - 层次清晰的背景
        'bg_primary': '#FFFFFF',   # 纯白主背景
        'bg_secondary': '#F8FAFC', # 极浅灰背景
        'bg_tertiary': '#F1F5F9',  # 浅灰背景
        'bg_quaternary': '#E2E8F0', # 中灰背景
        'bg_card': '#FFFFFF',      # 卡片背景
        'bg_sidebar': '#F8FAFC',   # 侧边栏背景
        'bg_toolbar': '#FFFFFF',   # 工具栏背景

        # 边框和分割线 - 清晰的视觉分割
        'border': '#E2E8F0',       # 主边框色
        'border_light': '#F1F5F9', # 浅边框
        'border_medium': '#CBD5E1', # 中等边框
        'border_dark': '#94A3B8',  # 深边框
        'border_card': '#E2E8F0',  # 卡片边框
        'shadow': 'rgba(0,0,0,0.1)', # 阴影色
        'shadow_light': 'rgba(0,0,0,0.05)', # 浅阴影
        'shadow_medium': 'rgba(0,0,0,0.15)', # 中等阴影

        # 文字颜色 - 优化可读性
        'text_primary': '#1E293B',   # 主文字 - 深色，高对比度
        'text_secondary': '#475569', # 次文字 - 中等对比度
        'text_tertiary': '#64748B',  # 三级文字
        'text_muted': '#94A3B8',     # 弱化文字 - 低对比度
        'text_white': '#FFFFFF',     # 白色文字
        'text_accent': '#0EA5E9',    # 强调文字
        'text_success': '#059669',   # 成功文字
        'text_warning': '#D97706',   # 警告文字
        'text_danger': '#DC2626',    # 错误文字

        # 状态色 - 清晰的交互反馈
        'hover': '#F1F5F9',        # 悬停背景
        'hover_light': '#F8FAFC',  # 浅悬停背景
        'active': '#E2E8F0',       # 激活背景
        'selected': '#EFF6FF',     # 选中背景
        'selected_primary': '#DBEAFE', # 主色选中背景
        'disabled': '#F1F5F9',     # 禁用背景
        'disabled_text': '#94A3B8', # 禁用文字

        # 图表和数据可视化色彩
        'chart_1': '#0EA5E9',      # 图表色1 - 蓝色
        'chart_2': '#10B981',      # 图表色2 - 绿色
        'chart_3': '#8B5CF6',      # 图表色3 - 紫色
        'chart_4': '#F59E0B',      # 图表色4 - 橙色
        'chart_5': '#EF4444',      # 图表色5 - 红色
        'chart_6': '#64748B',      # 图表色6 - 灰色
    }
    
    # 字体配置 - 现代化字体系统
    FONTS = {
        'default': ('Microsoft YaHei UI', 9),
        'heading': ('Microsoft YaHei UI', 12, 'bold'),
        'subheading': ('Microsoft YaHei UI', 10, 'bold'),
        'small': ('Microsoft YaHei UI', 8),
        'tiny': ('Microsoft YaHei UI', 7),
        'large': ('Microsoft YaHei UI', 14, 'bold'),
        'xlarge': ('Microsoft YaHei UI', 16, 'bold'),
        'code': ('Consolas', 9),
        'button': ('Microsoft YaHei UI', 9, 'bold'),
        'caption': ('Microsoft YaHei UI', 8, 'italic'),
    }
    
    # 间距配置 - 现代化间距系统
    SPACING = {
        'xxs': 2,   # 极极小间距
        'xs': 4,    # 极小间距 - 紧密元素
        'sm': 8,    # 小间距 - 相关元素
        'md': 12,   # 中等间距 - 组件间距
        'lg': 16,   # 大间距 - 区块间距
        'xl': 24,   # 超大间距 - 主要分区
        'xxl': 32,  # 极大间距 - 页面级分区
        'xxxl': 48, # 超极大间距 - 顶级分区
        'card': 20, # 卡片内边距 - 增加舒适度
        'section': 28, # 区块间距 - 清晰分割
        'button': 12, # 按钮内边距
        'input': 10,  # 输入框内边距
        'list_item': 6, # 列表项间距
        'grid': 16,   # 网格间距
    }
    
    # 圆角配置 - 现代化圆角系统
    RADIUS = {
        'xs': 2,
        'sm': 4,
        'md': 8,
        'lg': 12,
        'xl': 16,
        'xxl': 20,
        'round': 50,  # 圆形
    }

    # 阴影配置 - 现代化阴影系统
    SHADOWS = {
        'none': 'none',
        'xs': '0 1px 2px rgba(0,0,0,0.05)',
        'sm': '0 1px 3px rgba(0,0,0,0.1)',
        'md': '0 4px 6px rgba(0,0,0,0.07)',
        'lg': '0 10px 15px rgba(0,0,0,0.1)',
        'xl': '0 20px 25px rgba(0,0,0,0.1)',
        'card': '0 2px 8px rgba(0,0,0,0.08)',
        'hover': '0 4px 12px rgba(0,0,0,0.12)',
    }

    @classmethod
    def configure_ttk_styles(cls, root):
        """配置ttk样式"""
        style = ttk.Style(root)
        
        # 配置主题
        style.theme_use('clam')
        
        # 配置通用样式
        style.configure('.',
                       font=cls.FONTS['default'],
                       background=cls.COLORS['bg_primary'])
        
        # 配置Frame样式
        style.configure('Card.TFrame',
                       background=cls.COLORS['bg_primary'],
                       relief='flat',
                       borderwidth=1)
        
        style.configure('Sidebar.TFrame',
                       background=cls.COLORS['bg_secondary'],
                       relief='flat')
        
        # 配置Label样式
        style.configure('Heading.TLabel',
                       font=cls.FONTS['heading'],
                       foreground=cls.COLORS['text_primary'],
                       background=cls.COLORS['bg_primary'])
        
        style.configure('Subheading.TLabel',
                       font=cls.FONTS['subheading'],
                       foreground=cls.COLORS['text_secondary'],
                       background=cls.COLORS['bg_primary'])
        
        style.configure('Muted.TLabel',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['text_muted'],
                       background=cls.COLORS['bg_primary'])
        
        # 配置Button样式 - 现代化按钮设计
        style.configure('Primary.TButton',
                       font=cls.FONTS['button'],
                       foreground=cls.COLORS['text_white'],
                       background=cls.COLORS['primary'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(cls.SPACING['lg'], cls.SPACING['md']))

        style.map('Primary.TButton',
                 background=[('active', cls.COLORS['primary_hover']),
                           ('pressed', cls.COLORS['primary_dark'])])

        style.configure('Secondary.TButton',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['text_primary'],
                       background=cls.COLORS['bg_secondary'],
                       borderwidth=1,
                       bordercolor=cls.COLORS['border'],
                       focuscolor='none',
                       relief='flat',
                       padding=(cls.SPACING['md'], cls.SPACING['sm']))

        style.map('Secondary.TButton',
                 background=[('active', cls.COLORS['hover']),
                           ('pressed', cls.COLORS['active'])])
        
        # 配置Entry样式
        style.configure('Modern.TEntry',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['text_primary'],
                       fieldbackground=cls.COLORS['bg_primary'],
                       borderwidth=1,
                       relief='solid',
                       padding=cls.SPACING['sm'])
        
        # 配置Treeview样式
        style.configure('Modern.Treeview',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['text_primary'],
                       background=cls.COLORS['bg_primary'],
                       fieldbackground=cls.COLORS['bg_primary'],
                       borderwidth=0,
                       rowheight=28)
        
        style.configure('Modern.Treeview.Heading',
                       font=cls.FONTS['subheading'],
                       foreground=cls.COLORS['text_secondary'],
                       background=cls.COLORS['bg_tertiary'],
                       borderwidth=0,
                       relief='flat')
        
        style.map('Modern.Treeview',
                 background=[('selected', cls.COLORS['selected'])],
                 foreground=[('selected', cls.COLORS['text_primary'])])
        
        # 配置Progressbar样式
        style.configure('Modern.Horizontal.TProgressbar',
                       background=cls.COLORS['primary'],
                       troughcolor=cls.COLORS['bg_tertiary'],
                       borderwidth=0,
                       lightcolor=cls.COLORS['primary'],
                       darkcolor=cls.COLORS['primary'])
        
        # 配置Notebook样式
        style.configure('Modern.TNotebook',
                       background=cls.COLORS['bg_primary'],
                       borderwidth=0,
                       tabmargins=[0, 0, 0, 0])

        style.configure('Modern.TNotebook.Tab',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['text_secondary'],
                       background=cls.COLORS['bg_secondary'],
                       padding=[cls.SPACING['md'], cls.SPACING['sm']],
                       borderwidth=0)

        # 配置Checkbutton样式
        style.configure('Modern.TCheckbutton',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['text_primary'],
                       background=cls.COLORS['bg_primary'],
                       focuscolor='none',
                       borderwidth=0,
                       relief='flat',
                       padding=cls.SPACING['xs'])

        style.map('Modern.TCheckbutton',
                 background=[('active', cls.COLORS['hover']),
                           ('pressed', cls.COLORS['active'])],
                 foreground=[('disabled', cls.COLORS['text_muted'])])

        # 配置Radiobutton样式
        style.configure('Modern.TRadiobutton',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['text_primary'],
                       background=cls.COLORS['bg_primary'],
                       focuscolor='none',
                       borderwidth=0,
                       relief='flat',
                       padding=cls.SPACING['xs'])

        style.map('Modern.TRadiobutton',
                 background=[('active', cls.COLORS['hover']),
                           ('pressed', cls.COLORS['active'])],
                 foreground=[('disabled', cls.COLORS['text_muted'])])
        
        style.map('Modern.TNotebook.Tab',
                 background=[('selected', cls.COLORS['bg_primary']),
                           ('active', cls.COLORS['hover'])],
                 foreground=[('selected', cls.COLORS['text_primary'])])

        # 配置LabelFrame样式 - 现代化设计
        style.configure('Modern.TLabelframe',
                       background=cls.COLORS['bg_primary'],
                       borderwidth=1,
                       relief='solid',
                       bordercolor=cls.COLORS['border'])

        style.configure('Modern.TLabelframe.Label',
                       font=cls.FONTS['subheading'],
                       foreground=cls.COLORS['text_secondary'],
                       background=cls.COLORS['bg_primary'])

        # 配置Separator样式
        style.configure('Modern.TSeparator',
                       background=cls.COLORS['border_light'])

        # 配置Combobox样式
        style.configure('Modern.TCombobox',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['text_primary'],
                       fieldbackground=cls.COLORS['bg_primary'],
                       borderwidth=1,
                       relief='solid',
                       arrowcolor=cls.COLORS['text_secondary'])

        style.map('Modern.TCombobox',
                 fieldbackground=[('readonly', cls.COLORS['bg_secondary']),
                                ('focus', cls.COLORS['bg_primary'])],
                 bordercolor=[('focus', cls.COLORS['primary'])])

        # 配置现代化卡片样式Frame
        style.configure('Card.TFrame',
                       background=cls.COLORS['bg_card'],
                       relief='flat',
                       borderwidth=0)

        # 配置信息标签样式
        style.configure('Info.TLabel',
                       font=cls.FONTS['default'],
                       foreground=cls.COLORS['info'],
                       background=cls.COLORS['bg_primary'])

        # 配置现代化成功按钮样式 - 优化对比度
        style.configure('Success.TButton',
                       font=cls.FONTS['button'],
                       foreground=cls.COLORS['text_white'],
                       background=cls.COLORS['success'],
                       borderwidth=1,
                       relief='flat',
                       focuscolor='none',
                       padding=(cls.SPACING['lg'], cls.SPACING['md']))

        style.map('Success.TButton',
                 background=[('active', cls.COLORS['success_dark']),
                           ('pressed', cls.COLORS['success_dark'])],
                 relief=[('pressed', 'flat')])

        # 配置现代化警告按钮样式 - 优化对比度
        style.configure('Warning.TButton',
                       font=cls.FONTS['button'],
                       foreground=cls.COLORS['text_white'],
                       background=cls.COLORS['warning'],
                       borderwidth=1,
                       relief='flat',
                       focuscolor='none',
                       padding=(cls.SPACING['lg'], cls.SPACING['md']))

        style.map('Warning.TButton',
                 background=[('active', cls.COLORS['warning_dark']),
                           ('pressed', cls.COLORS['warning_dark'])],
                 relief=[('pressed', 'flat')])

        # 配置现代化强调按钮样式 - 优化对比度
        style.configure('Accent.TButton',
                       font=cls.FONTS['button'],
                       foreground=cls.COLORS['text_white'],
                       background=cls.COLORS['accent'],
                       borderwidth=1,
                       relief='flat',
                       focuscolor='none',
                       padding=(cls.SPACING['lg'], cls.SPACING['md']))

        style.map('Accent.TButton',
                 background=[('active', cls.COLORS['accent_dark']),
                           ('pressed', cls.COLORS['accent_dark'])],
                 relief=[('pressed', 'flat')])

        # 配置现代化危险按钮样式 - 新增
        style.configure('Danger.TButton',
                       font=cls.FONTS['button'],
                       foreground=cls.COLORS['text_white'],
                       background=cls.COLORS['danger'],
                       borderwidth=1,
                       relief='flat',
                       focuscolor='none',
                       padding=(cls.SPACING['lg'], cls.SPACING['md']))

        style.map('Danger.TButton',
                 background=[('active', cls.COLORS['danger_dark']),
                           ('pressed', cls.COLORS['danger_dark'])],
                 relief=[('pressed', 'flat')])

        # 配置现代化轮廓按钮样式 - 新增
        style.configure('Outline.TButton',
                       font=cls.FONTS['button'],
                       foreground=cls.COLORS['primary'],
                       background=cls.COLORS['bg_card'],
                       borderwidth=2,
                       relief='solid',
                       focuscolor='none',
                       padding=(cls.SPACING['md'], cls.SPACING['sm']))

        style.map('Outline.TButton',
                 background=[('active', cls.COLORS['selected_primary']),
                           ('pressed', cls.COLORS['selected_primary'])],
                 foreground=[('active', cls.COLORS['primary_dark']),
                           ('pressed', cls.COLORS['primary_dark'])],
                 relief=[('pressed', 'solid')])

        return style

    @classmethod
    def create_separator(cls, parent, orient='horizontal'):
        """创建分割线"""
        separator = ttk.Separator(parent, orient=orient)
        return separator

    @classmethod
    def create_modern_card(cls, parent, title=None, **kwargs):
        """创建现代化卡片容器 - 参考图片设计"""
        # 主卡片容器
        card_frame = tk.Frame(parent,
                             bg=cls.COLORS['bg_card'],
                             relief='flat',
                             bd=0,
                             **kwargs)

        # 添加阴影效果（通过边框模拟）
        shadow_frame = tk.Frame(parent,
                               bg=cls.COLORS['border_light'],
                               height=2)

        if title:
            # 卡片标题
            title_frame = tk.Frame(card_frame,
                                  bg=cls.COLORS['bg_card'],
                                  height=40)
            title_frame.pack(fill=tk.X, padx=cls.SPACING['card'],
                           pady=(cls.SPACING['md'], cls.SPACING['xs']))
            title_frame.pack_propagate(False)

            title_label = tk.Label(title_frame,
                                  text=title,
                                  font=cls.FONTS['subheading'],
                                  fg=cls.COLORS['text_primary'],
                                  bg=cls.COLORS['bg_card'])
            title_label.pack(side=tk.LEFT, anchor='w')

            # 分割线
            separator = tk.Frame(card_frame,
                               bg=cls.COLORS['border_light'],
                               height=1)
            separator.pack(fill=tk.X, padx=cls.SPACING['card'])

        return card_frame

    @classmethod
    def create_progress_bar(cls, parent, value=0, maximum=100, color=None, **kwargs):
        """创建现代化进度条 - 参考图片设计"""
        if color is None:
            color = cls.COLORS['primary']

        # 进度条容器
        progress_frame = tk.Frame(parent,
                                 bg=cls.COLORS['bg_tertiary'],
                                 height=8,
                                 **kwargs)
        progress_frame.pack_propagate(False)

        # 进度条填充
        fill_frame = tk.Frame(progress_frame,
                             bg=color,
                             height=8)

        # 计算进度宽度
        def update_progress(new_value):
            if maximum > 0:
                width_percent = min(new_value / maximum, 1.0)
                fill_frame.place(x=0, y=0, relwidth=width_percent, height=8)

        # 初始化进度
        update_progress(value)

        # 添加更新方法
        progress_frame.update_progress = update_progress

        return progress_frame
    
    @classmethod
    def create_card_frame(cls, parent, **kwargs):
        """创建卡片式框架"""
        frame = ttk.Frame(parent, style='Card.TFrame', **kwargs)
        return frame
    
    @classmethod
    def create_sidebar_frame(cls, parent, **kwargs):
        """创建侧边栏框架"""
        frame = ttk.Frame(parent, style='Sidebar.TFrame', **kwargs)
        return frame


class IconManager:
    """图标管理器"""
    
    # 文件类型图标
    FILE_ICONS = {
        '.txt': '📄',
        '.doc': '📝',
        '.docx': '📝',
        '.pdf': '📕',
        'folder': '📁',
        'folder_open': '📂',
        'default': '📄'
    }
    
    # 操作图标 - 修复语义错误和重复问题
    ACTION_ICONS = {
        # 基础操作
        'refresh': '🔄',
        'search': '🔍',
        'filter': '🔽',
        'settings': '⚙️',

        # 文件和文件夹
        'folder': '📁',
        'folder_open': '📂',
        'file': '📄',
        'new_folder': '📁',
        'browse': '📂',

        # 编辑操作
        'copy': '📋',
        'cut': '✂️',
        'paste': '📌',
        'delete': '🗑️',
        'edit': '✏️',
        'save': '💾',

        # 导入导出
        'export': '📤',
        'import': '📥',
        'upload': '⬆️',
        'download': '⬇️',

        # 播放控制
        'play': '▶️',
        'pause': '⏸️',
        'stop': '⏹️',
        'start': '🚀',

        # 状态和反馈
        'success': '✅',
        'check': '☑️',
        'uncheck': '☐',
        'warning': '⚠️',
        'error': '❌',
        'info': 'ℹ️',

        # 选择操作 - 修复语义错误
        'select_all': '☑️',    # 全选使用复选框图标
        'clear_all': '🧹',     # 清除使用扫帚图标，而非❌
        'reset': '🔄',         # 重置使用刷新图标

        # 数量操作
        'plus': '➕',
        'minus': '➖',
        'add': '➕',
        'remove': '➖',

        # 展开折叠
        'expand': '📖',
        'collapse': '📕',
        'expand_all': '📚',
        'collapse_all': '📙',

        # 排序和组织
        'sort': '🔀',
        'sort_asc': '🔼',
        'sort_desc': '🔽',

        # 界面控制
        'menu': '☰',
        'close': '✖️',
        'minimize': '🗕',
        'maximize': '🗖',
        'fullscreen': '⛶',

        # 预览和查看
        'preview': '👁️',
        'view': '👀',
        'zoom_in': '🔍',
        'zoom_out': '🔎',

        # 配置和工具
        'config': '🔧',
        'tools': '🛠️',
        'help': '❓',
        'about': 'ℹ️',
    }
    
    @classmethod
    def get_file_icon(cls, filename_or_ext):
        """获取文件图标"""
        if filename_or_ext.startswith('.'):
            ext = filename_or_ext.lower()
        else:
            ext = os.path.splitext(filename_or_ext)[1].lower()
        return cls.FILE_ICONS.get(ext, cls.FILE_ICONS['default'])
    
    @classmethod
    def get_action_icon(cls, action):
        """获取操作图标"""
        return cls.ACTION_ICONS.get(action, '')


class AnimationHelper:
    """动画效果助手"""

    @staticmethod
    def fade_in(widget, duration=300, steps=20):
        """淡入效果"""
        alpha_step = 1.0 / steps
        delay = duration // steps

        def animate(step=0):
            if step <= steps:
                alpha = alpha_step * step
                # 这里可以实现透明度动画（需要特殊控件支持）
                widget.update_idletasks()
                if step < steps:
                    widget.after(delay, lambda: animate(step + 1))

        animate()

    @staticmethod
    def slide_in(widget, direction='left', duration=300):
        """滑入效果"""
        # 简化的滑入效果实现
        widget.pack()
        widget.update_idletasks()

    @staticmethod
    def pulse_button(button, color=None, duration=200):
        """按钮脉冲效果"""
        if not color:
            color = theme.COLORS['primary_light']

        original_bg = button.cget('background')

        def restore():
            try:
                button.configure(background=original_bg)
            except:
                pass

        try:
            button.configure(background=color)
            button.after(duration, restore)
        except:
            pass


class TooltipHelper:
    """工具提示助手"""

    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None

        self.widget.bind('<Enter>', self.on_enter)
        self.widget.bind('<Leave>', self.on_leave)

    def on_enter(self, event=None):
        """鼠标进入时显示提示"""
        if self.tooltip:
            return

        x, y, _, _ = self.widget.bbox("insert") if hasattr(self.widget, 'bbox') else (0, 0, 0, 0)
        x += self.widget.winfo_rootx() + 20
        y += self.widget.winfo_rooty() + 20

        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{x}+{y}")

        label = tk.Label(self.tooltip, text=self.text,
                        background=theme.COLORS['bg_dark'],
                        foreground=theme.COLORS['text_white'],
                        font=theme.FONTS['small'],
                        relief='solid', borderwidth=1,
                        padx=theme.SPACING['sm'], pady=theme.SPACING['xs'])
        label.pack()

    def on_leave(self, event=None):
        """鼠标离开时隐藏提示"""
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None


class NotificationHelper:
    """通知助手"""

    @staticmethod
    def show_toast(parent, message, duration=3000, type='info'):
        """显示Toast通知"""
        toast = tk.Toplevel(parent)
        toast.wm_overrideredirect(True)

        # 设置位置（右下角）
        parent.update_idletasks()
        x = parent.winfo_rootx() + parent.winfo_width() - 300
        y = parent.winfo_rooty() + parent.winfo_height() - 100
        toast.wm_geometry(f"280x60+{x}+{y}")

        # 设置样式
        bg_color = {
            'info': theme.COLORS['primary'],
            'success': theme.COLORS['success'],
            'warning': theme.COLORS['warning'],
            'error': theme.COLORS['danger']
        }.get(type, theme.COLORS['primary'])

        frame = tk.Frame(toast, bg=bg_color, padx=theme.SPACING['md'], pady=theme.SPACING['sm'])
        frame.pack(fill=tk.BOTH, expand=True)

        label = tk.Label(frame, text=message,
                        bg=bg_color, fg=theme.COLORS['text_white'],
                        font=theme.FONTS['default'], wraplength=250)
        label.pack(expand=True)

        # 自动关闭
        toast.after(duration, toast.destroy)

        # 淡入效果
        AnimationHelper.fade_in(toast)

        return toast


# 全局主题实例
theme = ModernTheme()
icons = IconManager()
animation = AnimationHelper()
notification = NotificationHelper()

('C:\\Users\\<USER>\\Desktop\\自动分类\\build\\文件自动分类工具2.0\\PYZ-00.pyz',
 [('Crypto',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Cipher.ARC4',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\ARC4.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kw',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kw.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kwp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kwp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Random',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyPDF2',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._cmap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_cmap.py',
   'PYMODULE'),
  ('PyPDF2._codecs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._codecs.adobe_glyphs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\adobe_glyphs.py',
   'PYMODULE'),
  ('PyPDF2._codecs.pdfdoc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\pdfdoc.py',
   'PYMODULE'),
  ('PyPDF2._codecs.std',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\std.py',
   'PYMODULE'),
  ('PyPDF2._codecs.symbol',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\symbol.py',
   'PYMODULE'),
  ('PyPDF2._codecs.zapfding',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\zapfding.py',
   'PYMODULE'),
  ('PyPDF2._encryption',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_encryption.py',
   'PYMODULE'),
  ('PyPDF2._merger',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_merger.py',
   'PYMODULE'),
  ('PyPDF2._page',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_page.py',
   'PYMODULE'),
  ('PyPDF2._protocols',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_protocols.py',
   'PYMODULE'),
  ('PyPDF2._reader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_reader.py',
   'PYMODULE'),
  ('PyPDF2._security',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_security.py',
   'PYMODULE'),
  ('PyPDF2._utils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._version',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_version.py',
   'PYMODULE'),
  ('PyPDF2._writer',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_writer.py',
   'PYMODULE'),
  ('PyPDF2.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\constants.py',
   'PYMODULE'),
  ('PyPDF2.errors',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\errors.py',
   'PYMODULE'),
  ('PyPDF2.filters',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\filters.py',
   'PYMODULE'),
  ('PyPDF2.generic',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.generic._annotations',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_annotations.py',
   'PYMODULE'),
  ('PyPDF2.generic._base',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_base.py',
   'PYMODULE'),
  ('PyPDF2.generic._data_structures',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_data_structures.py',
   'PYMODULE'),
  ('PyPDF2.generic._fit',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_fit.py',
   'PYMODULE'),
  ('PyPDF2.generic._outline',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_outline.py',
   'PYMODULE'),
  ('PyPDF2.generic._rectangle',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_rectangle.py',
   'PYMODULE'),
  ('PyPDF2.generic._utils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_utils.py',
   'PYMODULE'),
  ('PyPDF2.pagerange',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\pagerange.py',
   'PYMODULE'),
  ('PyPDF2.papersizes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\papersizes.py',
   'PYMODULE'),
  ('PyPDF2.types',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\types.py',
   'PYMODULE'),
  ('PyPDF2.xmp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\xmp.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_compression.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\bisect.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\calendar.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\cgi.py',
   'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.compat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\copy.py',
   'PYMODULE'),
  ('core',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\__init__.py',
   'PYMODULE'),
  ('core.config_manager',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\config_manager.py',
   'PYMODULE'),
  ('core.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\constants.py',
   'PYMODULE'),
  ('core.document_generator',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\document_generator.py',
   'PYMODULE'),
  ('core.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\exceptions.py',
   'PYMODULE'),
  ('core.file_processor',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\file_processor.py',
   'PYMODULE'),
  ('core.logger',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\logger.py',
   'PYMODULE'),
  ('core.security_validator',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\security_validator.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\doctest.py',
   'PYMODULE'),
  ('docx',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\__init__.py',
   'PYMODULE'),
  ('docx.api',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\api.py',
   'PYMODULE'),
  ('docx.blkcntnr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\blkcntnr.py',
   'PYMODULE'),
  ('docx.comments',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\comments.py',
   'PYMODULE'),
  ('docx.dml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\dml\\__init__.py',
   'PYMODULE'),
  ('docx.dml.color',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\dml\\color.py',
   'PYMODULE'),
  ('docx.document',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\document.py',
   'PYMODULE'),
  ('docx.drawing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\drawing\\__init__.py',
   'PYMODULE'),
  ('docx.enum',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\__init__.py',
   'PYMODULE'),
  ('docx.enum.base',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\base.py',
   'PYMODULE'),
  ('docx.enum.dml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\dml.py',
   'PYMODULE'),
  ('docx.enum.section',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\section.py',
   'PYMODULE'),
  ('docx.enum.shape',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\shape.py',
   'PYMODULE'),
  ('docx.enum.style',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\style.py',
   'PYMODULE'),
  ('docx.enum.table',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\table.py',
   'PYMODULE'),
  ('docx.enum.text',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\text.py',
   'PYMODULE'),
  ('docx.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\exceptions.py',
   'PYMODULE'),
  ('docx.image',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\__init__.py',
   'PYMODULE'),
  ('docx.image.bmp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\bmp.py',
   'PYMODULE'),
  ('docx.image.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\constants.py',
   'PYMODULE'),
  ('docx.image.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\exceptions.py',
   'PYMODULE'),
  ('docx.image.gif',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\gif.py',
   'PYMODULE'),
  ('docx.image.helpers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\helpers.py',
   'PYMODULE'),
  ('docx.image.image',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\image.py',
   'PYMODULE'),
  ('docx.image.jpeg',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\jpeg.py',
   'PYMODULE'),
  ('docx.image.png',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\png.py',
   'PYMODULE'),
  ('docx.image.tiff',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\tiff.py',
   'PYMODULE'),
  ('docx.opc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\__init__.py',
   'PYMODULE'),
  ('docx.opc.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\constants.py',
   'PYMODULE'),
  ('docx.opc.coreprops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\coreprops.py',
   'PYMODULE'),
  ('docx.opc.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\exceptions.py',
   'PYMODULE'),
  ('docx.opc.oxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\oxml.py',
   'PYMODULE'),
  ('docx.opc.package',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\package.py',
   'PYMODULE'),
  ('docx.opc.packuri',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\packuri.py',
   'PYMODULE'),
  ('docx.opc.part',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\part.py',
   'PYMODULE'),
  ('docx.opc.parts',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\parts\\__init__.py',
   'PYMODULE'),
  ('docx.opc.parts.coreprops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\parts\\coreprops.py',
   'PYMODULE'),
  ('docx.opc.phys_pkg',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\phys_pkg.py',
   'PYMODULE'),
  ('docx.opc.pkgreader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\pkgreader.py',
   'PYMODULE'),
  ('docx.opc.pkgwriter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\pkgwriter.py',
   'PYMODULE'),
  ('docx.opc.rel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\rel.py',
   'PYMODULE'),
  ('docx.opc.shared',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\shared.py',
   'PYMODULE'),
  ('docx.opc.spec',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\spec.py',
   'PYMODULE'),
  ('docx.oxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\__init__.py',
   'PYMODULE'),
  ('docx.oxml.comments',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\comments.py',
   'PYMODULE'),
  ('docx.oxml.coreprops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\coreprops.py',
   'PYMODULE'),
  ('docx.oxml.document',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\document.py',
   'PYMODULE'),
  ('docx.oxml.drawing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\drawing.py',
   'PYMODULE'),
  ('docx.oxml.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\exceptions.py',
   'PYMODULE'),
  ('docx.oxml.ns',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\ns.py',
   'PYMODULE'),
  ('docx.oxml.numbering',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\numbering.py',
   'PYMODULE'),
  ('docx.oxml.parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\parser.py',
   'PYMODULE'),
  ('docx.oxml.section',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\section.py',
   'PYMODULE'),
  ('docx.oxml.settings',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\settings.py',
   'PYMODULE'),
  ('docx.oxml.shape',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\shape.py',
   'PYMODULE'),
  ('docx.oxml.shared',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\shared.py',
   'PYMODULE'),
  ('docx.oxml.simpletypes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\simpletypes.py',
   'PYMODULE'),
  ('docx.oxml.styles',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\styles.py',
   'PYMODULE'),
  ('docx.oxml.table',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\table.py',
   'PYMODULE'),
  ('docx.oxml.text',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\__init__.py',
   'PYMODULE'),
  ('docx.oxml.text.font',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\font.py',
   'PYMODULE'),
  ('docx.oxml.text.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\hyperlink.py',
   'PYMODULE'),
  ('docx.oxml.text.pagebreak',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\pagebreak.py',
   'PYMODULE'),
  ('docx.oxml.text.paragraph',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\paragraph.py',
   'PYMODULE'),
  ('docx.oxml.text.parfmt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\parfmt.py',
   'PYMODULE'),
  ('docx.oxml.text.run',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\run.py',
   'PYMODULE'),
  ('docx.oxml.xmlchemy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\xmlchemy.py',
   'PYMODULE'),
  ('docx.package',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\package.py',
   'PYMODULE'),
  ('docx.parts',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\__init__.py',
   'PYMODULE'),
  ('docx.parts.comments',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\comments.py',
   'PYMODULE'),
  ('docx.parts.document',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\document.py',
   'PYMODULE'),
  ('docx.parts.hdrftr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\hdrftr.py',
   'PYMODULE'),
  ('docx.parts.image',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\image.py',
   'PYMODULE'),
  ('docx.parts.numbering',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\numbering.py',
   'PYMODULE'),
  ('docx.parts.settings',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\settings.py',
   'PYMODULE'),
  ('docx.parts.story',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\story.py',
   'PYMODULE'),
  ('docx.parts.styles',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\styles.py',
   'PYMODULE'),
  ('docx.section',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\section.py',
   'PYMODULE'),
  ('docx.settings',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\settings.py',
   'PYMODULE'),
  ('docx.shape',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\shape.py',
   'PYMODULE'),
  ('docx.shared',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\shared.py',
   'PYMODULE'),
  ('docx.styles',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\styles\\__init__.py',
   'PYMODULE'),
  ('docx.styles.latent',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\styles\\latent.py',
   'PYMODULE'),
  ('docx.styles.style',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\styles\\style.py',
   'PYMODULE'),
  ('docx.styles.styles',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\styles\\styles.py',
   'PYMODULE'),
  ('docx.table',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\table.py',
   'PYMODULE'),
  ('docx.text',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\__init__.py',
   'PYMODULE'),
  ('docx.text.font',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\font.py',
   'PYMODULE'),
  ('docx.text.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\hyperlink.py',
   'PYMODULE'),
  ('docx.text.pagebreak',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\pagebreak.py',
   'PYMODULE'),
  ('docx.text.paragraph',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\paragraph.py',
   'PYMODULE'),
  ('docx.text.parfmt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\parfmt.py',
   'PYMODULE'),
  ('docx.text.run',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\run.py',
   'PYMODULE'),
  ('docx.text.tabstops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\tabstops.py',
   'PYMODULE'),
  ('docx.types',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\types.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\http\\server.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\numbers.py',
   'PYMODULE'),
  ('olefile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\olefile\\__init__.py',
   'PYMODULE'),
  ('olefile.olefile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\olefile\\olefile.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\optparse.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\random.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\signal.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textract',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\textract\\__init__.py',
   'PYMODULE'),
  ('textract.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\textract\\exceptions.py',
   'PYMODULE'),
  ('textract.parsers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\textract\\parsers\\__init__.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\zipimport.py',
   'PYMODULE')])

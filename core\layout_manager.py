"""
现代化布局管理器
提供响应式布局和自适应界面组件
"""

import tkinter as tk
from tkinter import ttk
from .ui_theme import theme, icons


class ResponsiveLayout:
    """响应式布局管理器"""
    
    def __init__(self, root):
        self.root = root
        self.panels = {}
        self.splitters = {}
        
    def create_main_layout(self):
        """创建主布局结构"""
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # 顶部工具栏区域
        toolbar_frame = self.create_toolbar_area(main_container)
        toolbar_frame.pack(fill=tk.X, padx=theme.SPACING['md'], pady=(theme.SPACING['md'], theme.SPACING['xs']))

        # 主内容区域 - 优化间距
        content_frame = ttk.Frame(main_container)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=theme.SPACING['md'], pady=(0, theme.SPACING['xs']))

        # 创建三面板布局
        self.create_three_panel_layout(content_frame)

        # 底部状态栏
        status_frame = self.create_status_area(main_container)
        status_frame.pack(fill=tk.X, padx=theme.SPACING['md'], pady=(theme.SPACING['xs'], theme.SPACING['md']))

        return main_container
    
    def create_toolbar_area(self, parent):
        """创建工具栏区域"""
        toolbar_frame = theme.create_card_frame(parent)
        toolbar_frame.configure(padding=theme.SPACING['md'])

        # 左侧操作按钮组 - 改善对齐
        left_group = ttk.Frame(toolbar_frame)
        left_group.pack(side=tk.LEFT, fill=tk.Y, anchor='w')

        # 右侧设置按钮组 - 改善对齐
        right_group = ttk.Frame(toolbar_frame)
        right_group.pack(side=tk.RIGHT, fill=tk.Y, anchor='e')

        # 中间搜索区域 - 优化间距
        search_group = ttk.Frame(toolbar_frame)
        search_group.pack(side=tk.LEFT, fill=tk.BOTH, expand=True,
                         padx=(theme.SPACING['lg'], theme.SPACING['lg']))
        
        self.panels['toolbar'] = {
            'frame': toolbar_frame,
            'left_group': left_group,
            'right_group': right_group,
            'search_group': search_group
        }
        
        return toolbar_frame
    
    def create_three_panel_layout(self, parent):
        """创建优化的三面板布局"""
        # 主分割器（水平）- 优化分割器样式
        main_paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True)

        # 左侧面板（目录树）- 优化为30%，给目录树更多空间
        left_panel = self.create_left_panel(main_paned)
        main_paned.add(left_panel, weight=30)

        # 右侧分割器（垂直）
        right_paned = ttk.PanedWindow(main_paned, orient=tk.VERTICAL)
        main_paned.add(right_paned, weight=70)

        # 右上面板（文件列表）- 优化为65%，给文件列表更多空间
        center_panel = self.create_center_panel(right_paned)
        right_paned.add(center_panel, weight=65)

        # 右下面板（分类配置）- 35%
        bottom_panel = self.create_bottom_panel(right_paned)
        right_paned.add(bottom_panel, weight=35)
        
        self.splitters['main'] = main_paned
        self.splitters['right'] = right_paned
        
        return main_paned
    
    def create_left_panel(self, parent):
        """创建左侧目录面板"""
        panel_frame = theme.create_sidebar_frame(parent)

        # 面板标题 - 优化间距
        header_frame = ttk.Frame(panel_frame)
        header_frame.pack(fill=tk.X, padx=theme.SPACING['md'], pady=(theme.SPACING['md'], theme.SPACING['xs']))

        title_label = ttk.Label(header_frame, text="📁 目录浏览", style='Subheading.TLabel')
        title_label.pack(side=tk.LEFT, anchor='w')

        # 操作按钮 - 右对齐
        btn_frame = ttk.Frame(header_frame)
        btn_frame.pack(side=tk.RIGHT, anchor='e')

        # 分割线 - 优化间距
        separator = theme.create_separator(panel_frame)
        separator.pack(fill=tk.X, padx=theme.SPACING['md'], pady=(0, theme.SPACING['sm']))

        # 路径选择区域 - 优化间距
        path_frame = ttk.Frame(panel_frame)
        path_frame.pack(fill=tk.X, padx=theme.SPACING['md'], pady=(0, theme.SPACING['sm']))

        # 目录树容器 - 优化间距
        tree_frame = ttk.Frame(panel_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=theme.SPACING['md'], pady=(0, theme.SPACING['md']))
        
        self.panels['left'] = {
            'frame': panel_frame,
            'header': header_frame,
            'title': title_label,
            'buttons': btn_frame,
            'path_frame': path_frame,
            'tree_frame': tree_frame
        }
        
        return panel_frame
    
    def create_center_panel(self, parent):
        """创建中心文件列表面板"""
        panel_frame = theme.create_card_frame(parent)

        # 面板标题和控制区 - 优化间距
        header_frame = ttk.Frame(panel_frame)
        header_frame.pack(fill=tk.X, padx=theme.SPACING['md'], pady=(theme.SPACING['md'], theme.SPACING['xs']))

        # 左侧标题 - 改善对齐
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(side=tk.LEFT, fill=tk.Y, anchor='w')

        title_label = ttk.Label(title_frame, text="📄 文件列表", style='Subheading.TLabel')
        title_label.pack(side=tk.LEFT, anchor='w')

        # 右侧控制按钮 - 改善对齐
        control_frame = ttk.Frame(header_frame)
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, anchor='e')

        # 文件类型过滤器 - 优化间距
        filter_frame = ttk.Frame(panel_frame)
        filter_frame.pack(fill=tk.X, padx=theme.SPACING['md'], pady=(0, theme.SPACING['sm']))

        # 分割线 - 优化间距
        separator = theme.create_separator(panel_frame)
        separator.pack(fill=tk.X, padx=theme.SPACING['md'], pady=(0, theme.SPACING['sm']))

        # 文件列表容器 - 优化间距
        list_frame = ttk.Frame(panel_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=theme.SPACING['md'], pady=(0, theme.SPACING['md']))
        
        self.panels['center'] = {
            'frame': panel_frame,
            'header': header_frame,
            'title': title_label,
            'controls': control_frame,
            'filter_frame': filter_frame,
            'list_frame': list_frame
        }
        
        return panel_frame
    
    def create_bottom_panel(self, parent):
        """创建底部分类配置面板"""
        panel_frame = theme.create_card_frame(parent)
        
        # 使用Notebook创建标签页
        notebook = ttk.Notebook(panel_frame, style='Modern.TNotebook')
        notebook.pack(fill=tk.BOTH, expand=True, padx=theme.SPACING['sm'], pady=theme.SPACING['sm'])
        
        # 分类配置标签页
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="⚙️ 分类配置")
        
        # 处理进度标签页
        progress_frame = ttk.Frame(notebook)
        notebook.add(progress_frame, text="📊 处理进度")
        
        # 日志输出标签页
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="📝 日志输出")
        
        self.panels['bottom'] = {
            'frame': panel_frame,
            'notebook': notebook,
            'config_frame': config_frame,
            'progress_frame': progress_frame,
            'log_frame': log_frame
        }
        
        return panel_frame
    
    def create_status_area(self, parent):
        """创建状态栏区域"""
        status_frame = theme.create_card_frame(parent)
        status_frame.configure(padding=theme.SPACING['sm'])
        
        # 左侧状态信息
        left_status = ttk.Frame(status_frame)
        left_status.pack(side=tk.LEFT, fill=tk.Y)
        
        # 中间进度条
        progress_frame = ttk.Frame(status_frame)
        progress_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(theme.SPACING['lg'], theme.SPACING['lg']))
        
        # 右侧统计信息
        right_status = ttk.Frame(status_frame)
        right_status.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.panels['status'] = {
            'frame': status_frame,
            'left': left_status,
            'progress': progress_frame,
            'right': right_status
        }
        
        return status_frame
    
    def get_panel(self, panel_name):
        """获取面板引用"""
        return self.panels.get(panel_name, {})
    
    def adjust_panel_weights(self, left_weight=25, center_weight=45, bottom_weight=30):
        """调整面板权重比例"""
        if 'main' in self.splitters:
            # 调整左右分割比例
            self.splitters['main'].sashpos(0, int(self.root.winfo_width() * left_weight / 100))
        
        if 'right' in self.splitters:
            # 调整上下分割比例
            total_right = center_weight + bottom_weight
            center_ratio = center_weight / total_right
            self.splitters['right'].sashpos(0, int(self.root.winfo_height() * center_ratio))


class ComponentFactory:
    """UI组件工厂"""
    
    @staticmethod
    def create_icon_button(parent, text, icon, command=None, style='Secondary.TButton', tooltip=None, **kwargs):
        """创建带图标的按钮"""
        from core.ui_theme import TooltipHelper

        button_text = f"{icon} {text}" if icon else text
        button = ttk.Button(parent, text=button_text, command=command, style=style, **kwargs)

        # 添加工具提示
        if tooltip:
            TooltipHelper(button, tooltip)
        elif text and icon:  # 如果有文本和图标，使用文本作为提示
            TooltipHelper(button, text)

        return button
    
    @staticmethod
    def create_search_entry(parent, placeholder="搜索...", **kwargs):
        """创建搜索输入框"""
        entry = ttk.Entry(parent, style='Modern.TEntry', **kwargs)
        # 添加占位符效果
        entry.placeholder = placeholder
        return entry
    
    @staticmethod
    def create_modern_treeview(parent, columns, **kwargs):
        """创建现代化树形视图"""
        # 创建容器框架
        container = ttk.Frame(parent)

        tree = ttk.Treeview(container, columns=columns, style='Modern.Treeview', **kwargs)

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(container, orient=tk.VERTICAL, command=tree.yview)
        h_scrollbar = ttk.Scrollbar(container, orient=tk.HORIZONTAL, command=tree.xview)

        tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        container.grid_rowconfigure(0, weight=1)
        container.grid_columnconfigure(0, weight=1)

        # 容器使用pack布局
        container.pack(fill=tk.BOTH, expand=True)

        return tree, v_scrollbar, h_scrollbar
    
    @staticmethod
    def create_progress_bar(parent, **kwargs):
        """创建现代化进度条"""
        progress = ttk.Progressbar(parent, style='Modern.Horizontal.TProgressbar', **kwargs)
        return progress
    
    @staticmethod
    def create_status_label(parent, text="就绪", **kwargs):
        """创建状态标签"""
        label = ttk.Label(parent, text=text, style='Muted.TLabel', **kwargs)
        return label

"""
应用程序常量配置
集中管理所有硬编码值和配置常量
"""

# 应用程序信息
APP_NAME = "文件自动分类工具"
APP_VERSION = "v2.1"
APP_TITLE = f"{APP_NAME} {APP_VERSION}"

# 窗口配置
DEFAULT_WINDOW_WIDTH = 1200
DEFAULT_WINDOW_HEIGHT = 700
MIN_WINDOW_WIDTH = 1000
MIN_WINDOW_HEIGHT = 600

# 文件处理配置
MAX_FILE_SIZE_MB = 100  # 最大文件大小限制（MB）
CONTENT_CACHE_SIZE = 128  # 内容缓存大小
CHUNK_SIZE = 8192  # 文件读取块大小
MAX_CONTENT_LENGTH = 1024 * 1024  # 最大内容长度（1MB）
MEMORY_CLEANUP_INTERVAL = 50  # 每处理多少个文件后清理内存

# 多线程配置
MIN_WORKERS = 2
MAX_WORKERS = 16
PARALLEL_THRESHOLD_FILES = 10  # 超过此数量使用并行处理
PARALLEL_THRESHOLD_SIZE = 10 * 1024 * 1024  # 超过此大小使用并行处理

# UI配置
BATCH_DISPLAY_SIZE = 50  # 分批显示文件的批次大小
PROGRESS_UPDATE_INTERVAL = 10  # 进度更新间隔（毫秒）

# 文件格式配置
SUPPORTED_EXTENSIONS = {'.txt', '.doc', '.docx', '.pdf'}
DEFAULT_EXTENSIONS = ['.txt', '.doc', '.docx', '.pdf']

# 编码配置
TEXT_ENCODINGS = ['utf-8', 'gbk', 'gb2312', 'utf-16']

# 日志配置
LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5
LOG_DIR = 'logs'
DEFAULT_LOG_FILE = 'file_sorter.log'

# 分类配置
DEFAULT_CATEGORIES = [
    {
        "name": "美盟方向",
        "keywords": ["美国", "北约", "盟友"],
        "priority": 1,
        "match_type": "contains"
    },
    {
        "name": "中东方向", 
        "keywords": ["中东", "伊朗", "以色列"],
        "priority": 2,
        "match_type": "contains"
    },
    {
        "name": "台湾方向",
        "keywords": ["台湾", "台海", "两岸"],
        "priority": 3,
        "match_type": "contains"
    },
    {
        "name": "涉我方向",
        "keywords": ["中国", "中方", "我国"],
        "priority": 4,
        "match_type": "contains"
    },
    {
        "name": "维和方向",
        "keywords": ["维和", "联合国", "和平"],
        "priority": 5,
        "match_type": "contains"
    },
    {
        "name": "其他",
        "keywords": [],
        "priority": 999,
        "match_type": "default"
    }
]

# 文件图标配置
FILE_ICONS = {
    '.txt': '📄',
    '.doc': '📝',
    '.docx': '📝',
    '.pdf': '📕',
    'default': '📄'
}

# 分类颜色配置
CATEGORY_COLORS = {
    '美盟方向': 'blue',
    '中东方向': 'orange',
    '台湾方向': 'red',
    '涉我方向': 'green',
    '维和方向': 'purple',
    '其他': 'gray',
    'default': 'black'
}

# 默认目录配置
DEFAULT_SOURCE_DIR_NAME = "待分类"
DEFAULT_TARGET_DIR_NAME = "已分类"

# 性能监控配置
PERFORMANCE_LOG_THRESHOLD = 5.0  # 超过5秒的操作记录性能日志
CACHE_HIT_RATE_THRESHOLD = 80  # 缓存命中率阈值

# 错误处理配置
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 1

# 备份配置
CONFIG_BACKUP_COUNT = 5
AUTO_BACKUP_ENABLED = True

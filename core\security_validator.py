"""
安全验证模块
提供文件操作安全检查和路径验证功能
"""

import os
import re
import logging
from pathlib import Path
from typing import List, Optional, Tuple
from .constants import MAX_FILE_SIZE_MB, SUPPORTED_EXTENSIONS


class SecurityValidator:
    """安全验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 危险路径模式
        self.dangerous_patterns = [
            r'\.\.[\\/]',  # 路径遍历
            r'^[\\/]',     # 绝对路径开头
            r'[\\/]\.\.[\\/]',  # 中间路径遍历
            r'[\\/]\.\.$',      # 结尾路径遍历
        ]
        
        # 系统敏感目录
        self.system_dirs = {
            'windows': [
                'C:\\Windows', 'C:\\Program Files', 'C:\\Program Files (x86)',
                'C:\\System Volume Information', 'C:\\$Recycle.Bin'
            ],
            'linux': [
                '/bin', '/sbin', '/usr/bin', '/usr/sbin', '/etc', '/root',
                '/sys', '/proc', '/dev'
            ]
        }
    
    def validate_file_path(self, file_path: str) -> Tuple[bool, str]:
        """
        验证文件路径安全性
        
        Args:
            file_path: 文件路径
            
        Returns:
            (is_valid, error_message)
        """
        try:
            # 检查路径是否为空
            if not file_path or not file_path.strip():
                return False, "文件路径不能为空"
            
            # 规范化路径
            normalized_path = os.path.normpath(file_path)
            
            # 检查危险路径模式
            for pattern in self.dangerous_patterns:
                if re.search(pattern, normalized_path):
                    return False, f"检测到危险路径模式: {pattern}"
            
            # 检查是否在系统敏感目录
            if self._is_system_directory(normalized_path):
                return False, "不允许访问系统敏感目录"
            
            # 检查路径长度
            if len(normalized_path) > 260:  # Windows路径长度限制
                return False, "文件路径过长"
            
            return True, ""
            
        except Exception as e:
            self.logger.error(f"路径验证失败: {e}")
            return False, f"路径验证失败: {str(e)}"
    
    def validate_file_operation(self, source_path: str, target_path: str, 
                              operation: str = "copy") -> Tuple[bool, str]:
        """
        验证文件操作安全性
        
        Args:
            source_path: 源文件路径
            target_path: 目标文件路径
            operation: 操作类型 (copy, move, delete)
            
        Returns:
            (is_valid, error_message)
        """
        try:
            # 验证源路径
            is_valid, error_msg = self.validate_file_path(source_path)
            if not is_valid:
                return False, f"源路径无效: {error_msg}"
            
            # 验证目标路径（删除操作不需要）
            if operation != "delete":
                is_valid, error_msg = self.validate_file_path(target_path)
                if not is_valid:
                    return False, f"目标路径无效: {error_msg}"
            
            # 检查源文件是否存在
            if not os.path.exists(source_path):
                return False, "源文件不存在"
            
            # 检查文件大小
            if os.path.isfile(source_path):
                file_size = os.path.getsize(source_path)
                max_size = MAX_FILE_SIZE_MB * 1024 * 1024
                if file_size > max_size:
                    return False, f"文件大小超过限制 ({MAX_FILE_SIZE_MB}MB)"
            
            # 检查文件扩展名
            if os.path.isfile(source_path):
                ext = os.path.splitext(source_path)[1].lower()
                if ext not in SUPPORTED_EXTENSIONS:
                    return False, f"不支持的文件类型: {ext}"
            
            # 检查目标目录权限（非删除操作）
            if operation != "delete":
                target_dir = os.path.dirname(target_path)
                if not os.access(target_dir, os.W_OK):
                    return False, "目标目录没有写入权限"
            
            return True, ""
            
        except Exception as e:
            self.logger.error(f"文件操作验证失败: {e}")
            return False, f"文件操作验证失败: {str(e)}"
    
    def validate_directory_access(self, dir_path: str) -> Tuple[bool, str]:
        """
        验证目录访问权限
        
        Args:
            dir_path: 目录路径
            
        Returns:
            (is_valid, error_message)
        """
        try:
            # 验证路径
            is_valid, error_msg = self.validate_file_path(dir_path)
            if not is_valid:
                return False, error_msg
            
            # 检查目录是否存在
            if not os.path.exists(dir_path):
                return False, "目录不存在"
            
            # 检查是否为目录
            if not os.path.isdir(dir_path):
                return False, "路径不是目录"
            
            # 检查读取权限
            if not os.access(dir_path, os.R_OK):
                return False, "目录没有读取权限"
            
            return True, ""
            
        except Exception as e:
            self.logger.error(f"目录访问验证失败: {e}")
            return False, f"目录访问验证失败: {str(e)}"
    
    def _is_system_directory(self, path: str) -> bool:
        """检查是否为系统敏感目录"""
        try:
            normalized_path = os.path.normpath(path).upper()
            
            # Windows系统目录检查
            for sys_dir in self.system_dirs['windows']:
                if normalized_path.startswith(sys_dir.upper()):
                    return True
            
            # Linux系统目录检查
            for sys_dir in self.system_dirs['linux']:
                if normalized_path.startswith(sys_dir.upper()):
                    return True
            
            return False
            
        except Exception:
            return True  # 出错时保守处理，认为是系统目录
    
    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除危险字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        try:
            # 移除危险字符
            dangerous_chars = r'[<>:"/\\|?*]'
            clean_name = re.sub(dangerous_chars, '_', filename)
            
            # 移除控制字符
            clean_name = ''.join(char for char in clean_name if ord(char) >= 32)
            
            # 限制长度
            if len(clean_name) > 200:
                name, ext = os.path.splitext(clean_name)
                clean_name = name[:200-len(ext)] + ext
            
            # 确保不为空
            if not clean_name.strip():
                clean_name = "unnamed_file"
            
            return clean_name.strip()
            
        except Exception as e:
            self.logger.error(f"文件名清理失败: {e}")
            return "unnamed_file"
    
    def check_disk_space(self, path: str, required_bytes: int) -> Tuple[bool, str]:
        """
        检查磁盘空间是否足够
        
        Args:
            path: 检查路径
            required_bytes: 需要的字节数
            
        Returns:
            (is_sufficient, error_message)
        """
        try:
            # 获取磁盘使用情况
            if os.name == 'nt':  # Windows
                import shutil
                total, used, free = shutil.disk_usage(path)
            else:  # Unix/Linux
                statvfs = os.statvfs(path)
                free = statvfs.f_frsize * statvfs.f_bavail
            
            if free < required_bytes:
                free_mb = free / (1024 * 1024)
                required_mb = required_bytes / (1024 * 1024)
                return False, f"磁盘空间不足。可用: {free_mb:.1f}MB, 需要: {required_mb:.1f}MB"
            
            return True, ""
            
        except Exception as e:
            self.logger.error(f"磁盘空间检查失败: {e}")
            return False, f"磁盘空间检查失败: {str(e)}"


# 全局安全验证器实例
security_validator = SecurityValidator()

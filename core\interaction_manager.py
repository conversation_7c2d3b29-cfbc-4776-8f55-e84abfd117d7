"""
交互体验管理器
提供悬停效果、状态反馈、拖拽操作等交互功能
"""

import tkinter as tk
from tkinter import ttk
from .ui_theme import theme, animation, notification


class HoverEffectManager:
    """悬停效果管理器"""

    def __init__(self):
        self.hover_widgets = {}
        self.animation_jobs = {}

    def add_hover_effect(self, widget, hover_style=None, normal_style=None, animation_duration=150):
        """为控件添加悬停效果"""
        if hover_style is None:
            hover_style = {'background': theme.COLORS['hover']}
        if normal_style is None:
            normal_style = {'background': theme.COLORS['bg_primary']}

        self.hover_widgets[widget] = {
            'hover': hover_style,
            'normal': normal_style,
            'duration': animation_duration
        }

        widget.bind('<Enter>', lambda e: self._on_enter(widget))
        widget.bind('<Leave>', lambda e: self._on_leave(widget))

    def add_button_hover_effect(self, button):
        """为按钮添加专门的悬停效果"""
        button.bind('<Enter>', lambda e: self._button_on_enter(button))
        button.bind('<Leave>', lambda e: self._button_on_leave(button))
        button.bind('<Button-1>', lambda e: self._button_on_click(button))
        button.bind('<ButtonRelease-1>', lambda e: self._button_on_release(button))

    def _on_enter(self, widget):
        """鼠标进入时的效果"""
        try:
            # 取消之前的动画
            if widget in self.animation_jobs:
                widget.after_cancel(self.animation_jobs[widget])

            styles = self.hover_widgets[widget]['hover']
            for key, value in styles.items():
                widget.configure(**{key: value})

            # 添加轻微的缩放效果（通过改变relief实现）
            if hasattr(widget, 'configure'):
                try:
                    widget.configure(relief='raised')
                except:
                    pass
        except:
            pass

    def _on_leave(self, widget):
        """鼠标离开时的效果"""
        try:
            # 取消之前的动画
            if widget in self.animation_jobs:
                widget.after_cancel(self.animation_jobs[widget])

            styles = self.hover_widgets[widget]['normal']
            for key, value in styles.items():
                widget.configure(**{key: value})

            # 恢复正常状态
            if hasattr(widget, 'configure'):
                try:
                    widget.configure(relief='flat')
                except:
                    pass
        except:
            pass

    def _button_on_enter(self, button):
        """按钮鼠标进入效果"""
        try:
            button.configure(cursor='hand2')
        except:
            pass

    def _button_on_leave(self, button):
        """按钮鼠标离开效果"""
        try:
            button.configure(cursor='')
        except:
            pass

    def _button_on_click(self, button):
        """按钮点击效果"""
        try:
            button.configure(relief='sunken')
        except:
            pass

    def _button_on_release(self, button):
        """按钮释放效果"""
        try:
            button.configure(relief='raised')
            # 延迟恢复到正常状态
            button.after(100, lambda: button.configure(relief='flat'))
        except:
            pass


class FeedbackManager:
    """操作反馈管理器"""

    def __init__(self, parent):
        self.parent = parent
        self.feedback_queue = []
        self.loading_widgets = []

    def show_operation_feedback(self, message, type='info', duration=2000):
        """显示操作反馈"""
        return notification.show_toast(self.parent, message, duration, type)

    def show_progress_feedback(self, message, progress=None):
        """显示进度反馈"""
        # 这里可以实现进度条反馈
        pass

    def show_button_feedback(self, button, type='success'):
        """显示按钮操作反馈"""
        animation.pulse_button(button,
                             theme.COLORS['success'] if type == 'success' else theme.COLORS['danger'])

    def show_loading_indicator(self, parent, text="处理中..."):
        """显示加载指示器"""
        loading_frame = tk.Toplevel(parent)
        loading_frame.title("")
        loading_frame.geometry("250x100")
        loading_frame.resizable(False, False)
        loading_frame.transient(parent)
        loading_frame.grab_set()

        # 居中显示
        loading_frame.geometry("+%d+%d" % (
            parent.winfo_rootx() + parent.winfo_width()//2 - 125,
            parent.winfo_rooty() + parent.winfo_height()//2 - 50
        ))

        # 设置样式
        loading_frame.configure(bg=theme.COLORS['bg_primary'])

        # 加载文本
        label = ttk.Label(loading_frame, text=text, font=theme.FONTS['default'],
                         style='Heading.TLabel')
        label.pack(expand=True, pady=(20, 10))

        # 进度条
        progress = ttk.Progressbar(loading_frame, mode='indeterminate',
                                 style='Modern.Horizontal.TProgressbar')
        progress.pack(fill=tk.X, padx=30, pady=(0, 20))
        progress.start(10)

        self.loading_widgets.append(loading_frame)
        return loading_frame

    def hide_loading_indicator(self, loading_widget):
        """隐藏加载指示器"""
        if loading_widget in self.loading_widgets:
            self.loading_widgets.remove(loading_widget)
        try:
            loading_widget.destroy()
        except:
            pass

    def show_success_animation(self, widget):
        """显示成功动画"""
        original_bg = None
        try:
            original_bg = widget.cget('background')
        except:
            original_bg = theme.COLORS['bg_primary']

        def animate_success(step=0):
            if step < 4:
                # 绿色闪烁效果
                color = theme.COLORS['success'] if step % 2 == 0 else original_bg
                try:
                    widget.configure(background=color)
                except:
                    pass
                widget.after(150, lambda: animate_success(step + 1))
            else:
                # 恢复原始颜色
                try:
                    widget.configure(background=original_bg)
                except:
                    pass

        animate_success()

    def show_error_animation(self, widget):
        """显示错误动画"""
        original_bg = None
        try:
            original_bg = widget.cget('background')
        except:
            original_bg = theme.COLORS['bg_primary']

        def animate_error(step=0):
            if step < 4:
                # 红色闪烁效果
                color = theme.COLORS['danger'] if step % 2 == 0 else original_bg
                try:
                    widget.configure(background=color)
                except:
                    pass
                widget.after(150, lambda: animate_error(step + 1))
            else:
                # 恢复原始颜色
                try:
                    widget.configure(background=original_bg)
                except:
                    pass

        animate_error()


class DragDropManager:
    """拖拽操作管理器"""
    
    def __init__(self):
        self.drag_data = {}
    
    def enable_drag(self, widget, data_callback=None):
        """启用拖拽功能"""
        widget.bind('<Button-1>', lambda e: self._start_drag(e, widget, data_callback))
        widget.bind('<B1-Motion>', lambda e: self._on_drag(e, widget))
        widget.bind('<ButtonRelease-1>', lambda e: self._end_drag(e, widget))
    
    def enable_drop(self, widget, drop_callback=None):
        """启用放置功能"""
        widget.bind('<Button-1>', lambda e: self._on_drop(e, widget, drop_callback))
    
    def _start_drag(self, event, widget, data_callback):
        """开始拖拽"""
        self.drag_data['widget'] = widget
        self.drag_data['start_x'] = event.x
        self.drag_data['start_y'] = event.y
        if data_callback:
            self.drag_data['data'] = data_callback()
    
    def _on_drag(self, event, widget):
        """拖拽过程中"""
        # 可以在这里实现拖拽视觉反馈
        pass
    
    def _end_drag(self, event, widget):
        """结束拖拽"""
        self.drag_data.clear()
    
    def _on_drop(self, event, widget, drop_callback):
        """处理放置"""
        if drop_callback and 'data' in self.drag_data:
            drop_callback(self.drag_data['data'])


class StatusIndicatorManager:
    """状态指示器管理器"""
    
    def __init__(self):
        self.indicators = {}
    
    def create_status_indicator(self, parent, name, initial_status='normal'):
        """创建状态指示器"""
        indicator = tk.Label(parent, text="●", font=theme.FONTS['small'])
        self.indicators[name] = indicator
        self.update_status(name, initial_status)
        return indicator
    
    def update_status(self, name, status):
        """更新状态"""
        if name not in self.indicators:
            return
        
        indicator = self.indicators[name]
        colors = {
            'normal': theme.COLORS['text_muted'],
            'success': theme.COLORS['success'],
            'warning': theme.COLORS['warning'],
            'error': theme.COLORS['danger'],
            'processing': theme.COLORS['primary']
        }
        
        color = colors.get(status, theme.COLORS['text_muted'])
        indicator.configure(fg=color)


class ContextMenuManager:
    """右键菜单管理器"""
    
    def __init__(self):
        self.menus = {}
    
    def create_context_menu(self, widget, menu_items):
        """创建右键菜单"""
        menu = tk.Menu(widget, tearoff=0)
        
        for item in menu_items:
            if item == 'separator':
                menu.add_separator()
            else:
                menu.add_command(label=item['label'], command=item['command'])
        
        def show_menu(event):
            try:
                menu.tk_popup(event.x_root, event.y_root)
            finally:
                menu.grab_release()
        
        widget.bind('<Button-3>', show_menu)
        self.menus[widget] = menu
        return menu


class InteractionManager:
    """交互管理器主类"""
    
    def __init__(self, parent):
        self.parent = parent
        self.hover_manager = HoverEffectManager()
        self.feedback_manager = FeedbackManager(parent)
        self.drag_drop_manager = DragDropManager()
        self.status_manager = StatusIndicatorManager()
        self.context_menu_manager = ContextMenuManager()
    
    def enhance_widget_interaction(self, widget, widget_type='button'):
        """增强控件交互体验"""
        if widget_type == 'button':
            self.hover_manager.add_hover_effect(widget)
        elif widget_type == 'listitem':
            self.hover_manager.add_hover_effect(widget, 
                                               {'background': theme.COLORS['hover']},
                                               {'background': theme.COLORS['bg_primary']})
    
    def add_operation_feedback(self, operation_name, success_message, error_message=None):
        """添加操作反馈"""
        def feedback_wrapper(func):
            def wrapper(*args, **kwargs):
                try:
                    result = func(*args, **kwargs)
                    self.feedback_manager.show_operation_feedback(success_message, 'success')
                    return result
                except Exception as e:
                    error_msg = error_message or f"操作失败: {str(e)}"
                    self.feedback_manager.show_operation_feedback(error_msg, 'error')
                    raise
            return wrapper
        return feedback_wrapper
    
    def create_enhanced_treeview(self, parent, **kwargs):
        """创建增强的树形视图"""
        tree = ttk.Treeview(parent, **kwargs)
        
        # 添加悬停效果
        def on_motion(event):
            item = tree.identify_row(event.y)
            if item:
                tree.selection_set(item)
        
        tree.bind('<Motion>', on_motion)
        return tree


# 全局交互管理器实例
def create_interaction_manager(parent):
    """创建交互管理器实例"""
    return InteractionManager(parent)

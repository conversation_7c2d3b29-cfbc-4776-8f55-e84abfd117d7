"""
文件处理模块
负责文件内容提取、分类、移动/复制等操作
"""

import os
import shutil
import hashlib
import logging
import gc
import time
from typing import List, Tuple, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 文档处理库
import docx
import PyPDF2

# 尝试导入textract，如果失败则使用替代方案
try:
    import textract
    HAS_TEXTRACT = True
except ImportError:
    HAS_TEXTRACT = False
    logging.warning("textract未安装，将无法处理.doc文件")

from .exceptions import ProcessingError, FileAccessError
from .config_manager import ConfigManager
from .security_validator import security_validator
from .constants import (
    MAX_FILE_SIZE_MB, CONTENT_CACHE_SIZE, CHUNK_SIZE,
    MAX_CONTENT_LENGTH, MEMORY_CLEANUP_INTERVAL,
    SUPPORTED_EXTENSIONS, TEXT_ENCODINGS, MIN_WORKERS, MAX_WORKERS,
    PARALLEL_THRESHOLD_FILES, PARALLEL_THRESHOLD_SIZE
)


class FileProcessor:
    """文件处理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.processed_files = 0
        self.total_files = 0
        self.processing = False
        self.cancel_requested = False
        self._lock = threading.Lock()

        # 支持的文件扩展名
        self.supported_extensions = SUPPORTED_EXTENSIONS

        # 性能监控
        self._start_time = None
        self._file_cache = {}  # 文件内容缓存
        self._cache_hits = 0
        self._cache_misses = 0
    
    def get_files_in_directory(self, directory: str, file_formats: List[str] = None) -> List[Dict[str, Any]]:
        """获取目录中的可处理文件列表"""
        if file_formats is None:
            file_formats = ['.txt', '.doc', '.docx', '.pdf']
        
        files = []
        try:
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                # 跳过文件夹、临时文件、脚本文件等
                if (os.path.isfile(item_path) and 
                    not item.startswith('~$') and 
                    not item.startswith('.') and
                    item not in ['file_sorter.py', 'file_sorter_gui.py', 'file_sorter_log.txt'] and
                    not item.startswith('文章汇总') and
                    os.path.splitext(item)[1].lower() in file_formats):
                    
                    # 获取文件信息
                    size = os.path.getsize(item_path)
                    category = self.config_manager.get_category_for_filename(item)
                    
                    files.append({
                        'name': item,
                        'path': item_path,
                        'size': size,
                        'size_str': self._format_size(size),
                        'category': category,
                        'status': '未处理'
                    })
                    
        except Exception as e:
            logging.error(f"扫描目录失败: {e}")
            raise ProcessingError(f"扫描目录失败: {str(e)}")
        
        return files
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    
    def extract_file_content(self, file_path: str) -> Tuple[str, str]:
        """提取文件内容（优化版本）"""
        # 安全验证
        is_valid, error_msg = security_validator.validate_file_path(file_path)
        if not is_valid:
            raise FileAccessError(f"文件路径安全验证失败: {error_msg}")

        # 检查缓存
        file_stat = os.stat(file_path)
        cache_key = f"{file_path}_{file_stat.st_mtime}_{file_stat.st_size}"

        if cache_key in self._file_cache:
            self._cache_hits += 1
            return self._file_cache[cache_key]

        self._cache_misses += 1

        # 检查文件大小
        file_size_mb = file_stat.st_size / (1024 * 1024)
        if file_size_mb > MAX_FILE_SIZE_MB:
            logging.warning(f"文件过大，跳过处理: {os.path.basename(file_path)} ({file_size_mb:.1f}MB)")
            title = os.path.splitext(os.path.basename(file_path))[0]
            return title, f"[文件过大，跳过处理: {file_size_mb:.1f}MB]"

        file_ext = os.path.splitext(file_path)[1].lower()
        content = ''

        try:
            if file_ext == '.txt':
                content = self._extract_txt_content(file_path)
            elif file_ext == '.docx':
                content = self._extract_docx_content(file_path)
            elif file_ext == '.doc':
                content = self._extract_doc_content(file_path)
            elif file_ext == '.pdf':
                content = self._extract_pdf_content(file_path)
            else:
                raise ProcessingError(f"不支持的文件格式: {file_ext}")

        except Exception as e:
            logging.error(f"提取文件 {os.path.basename(file_path)} 内容失败: {e}")
            content = f"[提取内容失败: {str(e)}]"

        # 文本标准化处理和长度限制
        if content:
            content = ' '.join(content.split())
            if len(content) > MAX_CONTENT_LENGTH:
                content = content[:MAX_CONTENT_LENGTH] + "...[内容已截断]"

        # 使用文件名作为标题
        title = os.path.splitext(os.path.basename(file_path))[0]
        result = (title, content)

        # 缓存结果（限制缓存大小）
        if len(self._file_cache) < CONTENT_CACHE_SIZE:
            self._file_cache[cache_key] = result

        return result
    
    def _extract_txt_content(self, file_path: str) -> str:
        """提取TXT文件内容（优化版本）"""
        encodings = TEXT_ENCODINGS

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                    # 分块读取大文件
                    content_chunks = []
                    total_size = 0

                    while total_size < MAX_CONTENT_LENGTH:
                        chunk = f.read(CHUNK_SIZE)
                        if not chunk:
                            break
                        content_chunks.append(chunk)
                        total_size += len(chunk)

                    return ''.join(content_chunks)
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logging.warning(f"读取文件 {file_path} 时出错 (编码: {encoding}): {e}")
                continue

        # 如果所有编码都失败，使用二进制模式读取
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(MAX_CONTENT_LENGTH)
                return raw_data.decode('utf-8', errors='ignore')
        except Exception as e:
            logging.error(f"二进制读取文件 {file_path} 失败: {e}")
            return f"[读取失败: {str(e)}]"
    
    def _extract_docx_content(self, file_path: str) -> str:
        """提取DOCX文件内容"""
        try:
            doc = docx.Document(file_path)
            return '\n'.join([para.text for para in doc.paragraphs])
        except Exception as e:
            raise ProcessingError(f"读取DOCX文件失败: {str(e)}")
    
    def _extract_doc_content(self, file_path: str) -> str:
        """提取DOC文件内容"""
        if not HAS_TEXTRACT:
            return "[DOC文件需要安装textract库才能读取]"

        try:
            content = textract.process(str(file_path)).decode('utf-8', errors='ignore')
            return content
        except Exception as e:
            logging.warning(f"使用textract读取DOC文件失败: {e}")
            return f"[DOC文件读取失败: {str(e)}]"
    
    def _extract_pdf_content(self, file_path: str) -> str:
        """提取PDF文件内容"""
        try:
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                content = []
                for page in reader.pages:
                    content.append(page.extract_text())
                return '\n'.join(content)
        except Exception as e:
            raise ProcessingError(f"读取PDF文件失败: {str(e)}")
    
    def create_category_directories(self, target_dir: str) -> None:
        """创建分类目录"""
        categories = self.config_manager.get_categories()

        for category in categories:
            category_path = os.path.join(target_dir, category['name'])
            try:
                os.makedirs(category_path, exist_ok=True)
                logging.info(f"创建/确认分类目录: {category_path}")
            except Exception as e:
                logging.error(f"创建目录失败: {category_path}, 错误: {e}")
                raise FileAccessError(f"创建目录失败: {str(e)}")
    
    def process_file(self, file_info: Dict[str, Any], target_dir: str, copy_mode: bool = True) -> Dict[str, Any]:
        """处理单个文件"""
        if self.cancel_requested:
            return file_info
        
        try:
            file_path = file_info['path']
            category = file_info['category']
            file_name = file_info['name']
            
            # 提取文件内容
            title, content = self.extract_file_content(file_path)
            
            # 目标目录和路径
            dest_dir = os.path.join(target_dir, category)
            dest_path = os.path.join(dest_dir, file_name)
            
            # 处理文件名冲突
            if os.path.exists(dest_path):
                dest_path = self._get_unique_filename(dest_path)
            
            # 复制或移动文件
            if copy_mode:
                shutil.copy2(file_path, dest_path)
                operation = "复制"
            else:
                shutil.move(file_path, dest_path)
                operation = "移动"
            
            # 更新文件信息
            file_info.update({
                'status': '已完成',
                'dest_path': dest_path,
                'title': title,
                'content': content,
                'operation': operation
            })
            
            logging.info(f"已{operation}: {file_name} -> {category}/{os.path.basename(dest_path)}")
            
        except Exception as e:
            file_info['status'] = '出错'
            file_info['error'] = str(e)
            logging.error(f"处理文件 {file_info['name']} 时出错: {e}")
        
        return file_info
    
    def _get_unique_filename(self, file_path: str) -> str:
        """获取唯一的文件名（避免覆盖）"""
        directory = os.path.dirname(file_path)
        name, ext = os.path.splitext(os.path.basename(file_path))
        
        counter = 1
        while True:
            new_name = f"{name}_{counter}{ext}"
            new_path = os.path.join(directory, new_name)
            if not os.path.exists(new_path):
                return new_path
            counter += 1
    
    def process_files_batch(self, files: List[Dict[str, Any]], target_dir: str,
                           copy_mode: bool = True, max_workers: int = 4,
                           progress_callback=None) -> List[Dict[str, Any]]:
        """批量处理文件（优化版本）"""
        # 安全验证目标目录
        is_valid, error_msg = security_validator.validate_directory_access(target_dir)
        if not is_valid:
            raise ProcessingError(f"目标目录安全验证失败: {error_msg}")

        # 检查磁盘空间
        total_size = sum(f.get('size', 0) for f in files)
        is_sufficient, space_error = security_validator.check_disk_space(target_dir, total_size)
        if not is_sufficient:
            raise ProcessingError(f"磁盘空间检查失败: {space_error}")

        self.processing = True
        self.cancel_requested = False
        self.processed_files = 0
        self.total_files = len(files)
        self._start_time = time.time()

        # 清理缓存
        self._clear_cache()

        # 创建分类目录
        self.create_category_directories(target_dir)

        processed_files = []

        try:
            # 记录开始处理
            logging.info(f"开始批量处理 {len(files)} 个文件，目标目录: {target_dir}")

            # 对于大文件或大量文件，使用多线程处理
            if len(files) > PARALLEL_THRESHOLD_FILES or any(f['size'] > PARALLEL_THRESHOLD_SIZE for f in files):
                processed_files = self._process_files_parallel(files, target_dir, copy_mode, max_workers, progress_callback)
            else:
                processed_files = self._process_files_sequential(files, target_dir, copy_mode, progress_callback)

            # 记录性能统计
            self._log_performance_stats()

        except Exception as e:
            logging.error(f"批量处理文件失败: {e}")
            raise ProcessingError(f"批量处理文件失败: {str(e)}")
        finally:
            self.processing = False
            # 最终清理
            self._cleanup_resources()

        return processed_files
    
    def _process_files_sequential(self, files: List[Dict[str, Any]], target_dir: str,
                                copy_mode: bool, progress_callback) -> List[Dict[str, Any]]:
        """顺序处理文件（优化版本）"""
        processed_files = []

        for i, file_info in enumerate(files):
            if self.cancel_requested:
                break

            processed_file = self.process_file(file_info, target_dir, copy_mode)
            processed_files.append(processed_file)

            with self._lock:
                self.processed_files += 1

            # 定期清理内存
            if i > 0 and i % MEMORY_CLEANUP_INTERVAL == 0:
                self._periodic_cleanup()

            if progress_callback:
                progress_callback(self.processed_files, self.total_files, file_info['name'])

        return processed_files
    
    def _process_files_parallel(self, files: List[Dict[str, Any]], target_dir: str,
                              copy_mode: bool, max_workers: int, progress_callback) -> List[Dict[str, Any]]:
        """并行处理文件"""
        processed_files = [None] * len(files)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(self.process_file, file_info, target_dir, copy_mode): i
                for i, file_info in enumerate(files)
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_index):
                if self.cancel_requested:
                    break
                
                index = future_to_index[future]
                try:
                    processed_file = future.result()
                    processed_files[index] = processed_file
                    
                    with self._lock:
                        self.processed_files += 1
                    
                    if progress_callback:
                        progress_callback(self.processed_files, self.total_files, processed_file['name'])
                        
                except Exception as e:
                    logging.error(f"处理文件时出错: {e}")
                    processed_files[index] = files[index].copy()
                    processed_files[index]['status'] = '出错'
                    processed_files[index]['error'] = str(e)
        
        return [f for f in processed_files if f is not None]
    
    def cancel_processing(self) -> None:
        """取消处理"""
        self.cancel_requested = True
        logging.info("用户请求取消文件处理")
    
    def get_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logging.error(f"计算文件哈希失败: {e}")
            return ""
    
    def find_duplicate_files(self, files: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """查找重复文件"""
        hash_to_files = {}
        
        for file_info in files:
            file_hash = self.get_file_hash(file_info['path'])
            if file_hash:
                if file_hash not in hash_to_files:
                    hash_to_files[file_hash] = []
                hash_to_files[file_hash].append(file_info)
        
        # 只返回有重复的文件组
        duplicates = {h: files for h, files in hash_to_files.items() if len(files) > 1}
        return duplicates

    def get_optimal_worker_count(self, file_count: int) -> int:
        """根据文件数量和系统资源获取最优工作线程数"""
        import os

        # 获取CPU核心数
        cpu_count = os.cpu_count() or 4

        # 根据文件数量调整
        if file_count < 10:
            return min(MIN_WORKERS, cpu_count)
        elif file_count < 50:
            return min(4, cpu_count)
        elif file_count < 200:
            return min(cpu_count, 8)
        else:
            return min(cpu_count * 2, MAX_WORKERS)  # 对于大量文件，可以使用更多线程

    def estimate_processing_time(self, files: List[Dict[str, Any]]) -> float:
        """估算处理时间（秒）"""
        total_size = sum(file_info.get('size', 0) for file_info in files)

        # 基于文件大小和数量的简单估算
        base_time_per_file = 0.1  # 每个文件基础处理时间
        time_per_mb = 0.05  # 每MB处理时间

        estimated_time = len(files) * base_time_per_file + (total_size / (1024 * 1024)) * time_per_mb

        return max(estimated_time, 1.0)  # 最少1秒

    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'total_files': self.total_files,
            'processed_files': self.processed_files,
            'success_rate': (self.processed_files / self.total_files * 100) if self.total_files > 0 else 0,
            'cancel_requested': self.cancel_requested
        }

    def optimize_batch_size(self, total_files: int, available_memory_mb: int = 1024) -> int:
        """根据可用内存优化批处理大小"""
        # 估算每个文件平均占用内存（包括内容缓存）
        avg_memory_per_file = 2  # MB

        # 计算最大批处理大小
        max_batch_size = max(1, available_memory_mb // avg_memory_per_file)

        # 限制批处理大小
        optimal_batch_size = min(max_batch_size, total_files, 100)

        return optimal_batch_size

    def _clear_cache(self):
        """清理文件缓存"""
        self._file_cache.clear()
        self._cache_hits = 0
        self._cache_misses = 0
        logging.debug("文件缓存已清理")

    def _periodic_cleanup(self):
        """定期内存清理"""
        # 清理部分缓存
        if len(self._file_cache) > CONTENT_CACHE_SIZE // 2:
            # 保留最近使用的一半缓存
            cache_items = list(self._file_cache.items())
            self._file_cache = dict(cache_items[-CONTENT_CACHE_SIZE//2:])

        # 强制垃圾回收
        gc.collect()
        logging.debug("执行定期内存清理")

    def _cleanup_resources(self):
        """清理所有资源"""
        self._clear_cache()
        gc.collect()
        logging.debug("资源清理完成")

    def _log_performance_stats(self):
        """记录性能统计"""
        if self._start_time:
            duration = time.time() - self._start_time
            cache_hit_rate = (self._cache_hits / (self._cache_hits + self._cache_misses) * 100) if (self._cache_hits + self._cache_misses) > 0 else 0

            logging.info(f"处理完成 - 耗时: {duration:.2f}秒, "
                        f"文件数: {self.processed_files}, "
                        f"平均: {duration/max(self.processed_files, 1):.2f}秒/文件, "
                        f"缓存命中率: {cache_hit_rate:.1f}%")

    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self._file_cache),
            'cache_hits': self._cache_hits,
            'cache_misses': self._cache_misses,
            'hit_rate': (self._cache_hits / (self._cache_hits + self._cache_misses) * 100) if (self._cache_hits + self._cache_misses) > 0 else 0
        }

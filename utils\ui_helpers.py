"""
UI辅助工具模块
提供界面相关的辅助功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import platform
import logging
from typing import Callable, Any, Optional, Dict, List


class UIHelpers:
    """UI辅助工具类"""
    
    @staticmethod
    def get_default_font():
        """获取默认字体"""
        if platform.system() == 'Windows':
            return ('微软雅黑', 9)
        else:
            return ('SimHei', 9)
    
    @staticmethod
    def setup_style(style: ttk.Style):
        """设置界面样式"""
        default_font = UIHelpers.get_default_font()
        
        style.configure('.', font=default_font)
        style.configure('TButton', font=default_font)
        style.configure('TLabel', font=default_font)
        style.configure('TFrame', background='#f0f0f0')
        style.configure('Heading.TLabel', font=(default_font[0], 16, 'bold'))
        
        # 设置进度条样式
        style.configure("TProgressbar", thickness=20)
    
    @staticmethod
    def center_window(window: tk.Toplevel, width: int, height: int):
        """居中显示窗口"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @staticmethod
    def show_error(title: str, message: str, parent=None):
        """显示错误消息"""
        logging.error(f"{title}: {message}")
        messagebox.showerror(title, message, parent=parent)
    
    @staticmethod
    def show_warning(title: str, message: str, parent=None):
        """显示警告消息"""
        logging.warning(f"{title}: {message}")
        messagebox.showwarning(title, message, parent=parent)
    
    @staticmethod
    def show_info(title: str, message: str, parent=None):
        """显示信息消息"""
        logging.info(f"{title}: {message}")
        messagebox.showinfo(title, message, parent=parent)
    
    @staticmethod
    def ask_yes_no(title: str, message: str, parent=None) -> bool:
        """询问是否确认"""
        return messagebox.askyesno(title, message, parent=parent)
    
    @staticmethod
    def create_tooltip(widget: tk.Widget, text: str):
        """为控件创建工具提示"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            
            label = tk.Label(tooltip, text=text, background="lightyellow", 
                           relief="solid", borderwidth=1, font=("Arial", 8))
            label.pack()
            
            widget.tooltip = tooltip
        
        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    @staticmethod
    def create_progress_dialog(parent: tk.Widget, title: str = "处理中...") -> 'ProgressDialog':
        """创建进度对话框"""
        return ProgressDialog(parent, title)
    
    @staticmethod
    def validate_input(value: str, input_type: str = 'text', **kwargs) -> bool:
        """验证输入"""
        if input_type == 'text':
            min_length = kwargs.get('min_length', 0)
            max_length = kwargs.get('max_length', 1000)
            return min_length <= len(value.strip()) <= max_length
        
        elif input_type == 'number':
            try:
                num = float(value)
                min_val = kwargs.get('min_val', float('-inf'))
                max_val = kwargs.get('max_val', float('inf'))
                return min_val <= num <= max_val
            except ValueError:
                return False
        
        elif input_type == 'integer':
            try:
                num = int(value)
                min_val = kwargs.get('min_val', -2147483648)
                max_val = kwargs.get('max_val', 2147483647)
                return min_val <= num <= max_val
            except ValueError:
                return False
        
        return True
    
    @staticmethod
    def create_scrollable_frame(parent: tk.Widget) -> tuple:
        """创建可滚动的框架"""
        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind("<MouseWheel>", _on_mousewheel)
        
        return canvas, scrollbar, scrollable_frame


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, parent: tk.Widget, title: str = "处理中..."):
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        UIHelpers.center_window(self.dialog, 400, 150)
        
        # 创建界面元素
        self._create_widgets()
        
        # 变量
        self.cancelled = False
    
    def _create_widgets(self):
        """创建界面元素"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="准备中...")
        self.status_label.pack(pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            variable=self.progress_var, 
            length=300, 
            mode='determinate'
        )
        self.progress_bar.pack(pady=(0, 10))
        
        # 进度文本
        self.progress_text = ttk.Label(main_frame, text="0%")
        self.progress_text.pack(pady=(0, 10))
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            main_frame, 
            text="取消", 
            command=self._on_cancel
        )
        self.cancel_button.pack()
    
    def update_progress(self, current: int, total: int, status: str = ""):
        """更新进度"""
        if total > 0:
            progress = (current / total) * 100
            self.progress_var.set(progress)
            self.progress_text.config(text=f"{progress:.1f}% ({current}/{total})")
        
        if status:
            self.status_label.config(text=status)
        
        self.dialog.update_idletasks()
    
    def _on_cancel(self):
        """取消操作"""
        self.cancelled = True
        self.dialog.destroy()
    
    def is_cancelled(self) -> bool:
        """检查是否被取消"""
        return self.cancelled
    
    def close(self):
        """关闭对话框"""
        if self.dialog.winfo_exists():
            self.dialog.destroy()


class FilePreviewDialog:
    """文件预览对话框"""
    
    def __init__(self, parent: tk.Widget, file_path: str, content: str):
        self.parent = parent
        self.file_path = file_path
        self.content = content
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"文件预览 - {file_path}")
        self.dialog.geometry("800x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        UIHelpers.center_window(self.dialog, 800, 600)
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面元素"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件信息
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_frame, text=f"文件: {self.file_path}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"内容长度: {len(self.content)} 字符").pack(anchor=tk.W)
        
        # 内容显示
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文本框和滚动条
        self.text_widget = tk.Text(
            content_frame, 
            wrap=tk.WORD, 
            font=('Consolas', 10),
            state=tk.DISABLED
        )
        
        scrollbar = ttk.Scrollbar(content_frame, orient=tk.VERTICAL, command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=scrollbar.set)
        
        self.text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 插入内容
        self.text_widget.config(state=tk.NORMAL)
        self.text_widget.insert(tk.END, self.content)
        self.text_widget.config(state=tk.DISABLED)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="关闭", command=self.dialog.destroy).pack(side=tk.RIGHT)


class CategoryConfigDialog:
    """分类配置对话框"""
    
    def __init__(self, parent: tk.Widget, category_data: Dict[str, Any] = None, 
                 existing_names: List[str] = None):
        self.parent = parent
        self.category_data = category_data or {}
        self.existing_names = existing_names or []
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("分类配置" if category_data else "添加分类")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        UIHelpers.center_window(self.dialog, 500, 400)
        
        self._create_widgets()
        self._load_data()
    
    def _create_widgets(self):
        """创建界面元素"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 分类名称
        ttk.Label(main_frame, text="分类名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=30)
        name_entry.grid(row=0, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # 匹配方式
        ttk.Label(main_frame, text="匹配方式:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.match_type_var = tk.StringVar(value='contains')
        
        match_frame = ttk.Frame(main_frame)
        match_frame.grid(row=1, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        ttk.Radiobutton(match_frame, text="包含关键词", 
                       variable=self.match_type_var, value='contains').pack(side=tk.LEFT)
        ttk.Radiobutton(match_frame, text="以关键词开头", 
                       variable=self.match_type_var, value='startswith').pack(side=tk.LEFT)
        ttk.Radiobutton(match_frame, text="默认分类", 
                       variable=self.match_type_var, value='default').pack(side=tk.LEFT)
        
        # 优先级
        ttk.Label(main_frame, text="优先级:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.priority_var = tk.IntVar(value=1)
        priority_spin = ttk.Spinbox(main_frame, from_=1, to=10, 
                                   textvariable=self.priority_var, width=10)
        priority_spin.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 关键词
        ttk.Label(main_frame, text="关键词 (每行一个):").grid(row=3, column=0, sticky=tk.NW, pady=5)
        
        keywords_frame = ttk.Frame(main_frame)
        keywords_frame.grid(row=3, column=1, columnspan=2, sticky=tk.W+tk.E, pady=5)
        
        self.keywords_text = tk.Text(keywords_frame, width=30, height=8)
        keywords_scroll = ttk.Scrollbar(keywords_frame, orient=tk.VERTICAL, 
                                       command=self.keywords_text.yview)
        self.keywords_text.configure(yscrollcommand=keywords_scroll.set)
        
        self.keywords_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        keywords_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT, padx=10)
    
    def _load_data(self):
        """加载数据"""
        if self.category_data:
            self.name_var.set(self.category_data.get('name', ''))
            self.match_type_var.set(self.category_data.get('match_type', 'contains'))
            self.priority_var.set(self.category_data.get('priority', 1))
            
            keywords = self.category_data.get('keywords', [])
            self.keywords_text.insert(tk.END, '\n'.join(keywords))
    
    def _on_ok(self):
        """确定按钮"""
        name = self.name_var.get().strip()
        if not name:
            UIHelpers.show_warning("警告", "分类名称不能为空", self.dialog)
            return
        
        # 检查名称冲突
        original_name = self.category_data.get('name', '') if self.category_data else ''
        if name != original_name and name in self.existing_names:
            UIHelpers.show_warning("警告", "此分类名称已存在", self.dialog)
            return
        
        # 获取关键词
        keywords_text = self.keywords_text.get('1.0', tk.END)
        keywords = [line.strip() for line in keywords_text.split('\n') if line.strip()]
        
        self.result = {
            'name': name,
            'keywords': keywords,
            'match_type': self.match_type_var.get(),
            'priority': self.priority_var.get()
        }
        
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消按钮"""
        self.result = None
        self.dialog.destroy()
    
    def get_result(self) -> Optional[Dict[str, Any]]:
        """获取结果"""
        return self.result

(['C:\\Users\\<USER>\\Desktop\\自动分类\\gui_new2.0.py'],
 ['C:\\Users\\<USER>\\Desktop\\自动分类'],
 [],
 [('C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.13 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:03:15) [MSC '
 'v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('gui_new2.0',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\gui_new2.0.py',
   'PYSOURCE')],
 [('multiprocessing.spawn',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\gzip.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\gettext.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\quopri.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\calendar.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\statistics.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\numbers.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\contextlib.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\string.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\hashlib.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\email\\header.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\bisect.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\http\\client.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\contextvars.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\base64.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\hmac.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\struct.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\selectors.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tempfile.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\inspect.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\bz2.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pathlib.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tokenize.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tarfile.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\subprocess.py',
   'PYMODULE'),
  ('core.logger',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\logger.py',
   'PYMODULE'),
  ('core',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\smtplib.py',
   'PYMODULE'),
  ('core.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\exceptions.py',
   'PYMODULE'),
  ('core.document_generator',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\document_generator.py',
   'PYMODULE'),
  ('core.config_manager',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\config_manager.py',
   'PYMODULE'),
  ('core.file_processor',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\file_processor.py',
   'PYMODULE'),
  ('core.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\constants.py',
   'PYMODULE'),
  ('core.security_validator',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\core\\security_validator.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('textract',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\textract\\__init__.py',
   'PYMODULE'),
  ('textract.parsers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\textract\\parsers\\__init__.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\glob.py',
   'PYMODULE'),
  ('textract.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\textract\\exceptions.py',
   'PYMODULE'),
  ('PyPDF2',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.papersizes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\papersizes.py',
   'PYMODULE'),
  ('PyPDF2.pagerange',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\pagerange.py',
   'PYMODULE'),
  ('PyPDF2.errors',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\errors.py',
   'PYMODULE'),
  ('PyPDF2._writer',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_writer.py',
   'PYMODULE'),
  ('PyPDF2.types',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\types.py',
   'PYMODULE'),
  ('PyPDF2.generic._outline',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_outline.py',
   'PYMODULE'),
  ('PyPDF2.generic._data_structures',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_data_structures.py',
   'PYMODULE'),
  ('PyPDF2.filters',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\filters.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('olefile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\olefile\\__init__.py',
   'PYMODULE'),
  ('olefile.olefile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\olefile\\olefile.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\optparse.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\__future__.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PyPDF2.xmp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\xmp.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.generic._utils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._codecs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._codecs.zapfding',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\zapfding.py',
   'PYMODULE'),
  ('PyPDF2._codecs.symbol',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\symbol.py',
   'PYMODULE'),
  ('PyPDF2._codecs.std',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\std.py',
   'PYMODULE'),
  ('PyPDF2._codecs.pdfdoc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\pdfdoc.py',
   'PYMODULE'),
  ('PyPDF2._codecs.adobe_glyphs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_codecs\\adobe_glyphs.py',
   'PYMODULE'),
  ('PyPDF2.generic._fit',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_fit.py',
   'PYMODULE'),
  ('PyPDF2._protocols',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_protocols.py',
   'PYMODULE'),
  ('PyPDF2.generic._base',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_base.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('PyPDF2.generic',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.generic._rectangle',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_rectangle.py',
   'PYMODULE'),
  ('PyPDF2.generic._annotations',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\generic\\_annotations.py',
   'PYMODULE'),
  ('PyPDF2.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\constants.py',
   'PYMODULE'),
  ('PyPDF2._utils',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._security',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_security.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\uuid.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\platform.py',
   'PYMODULE'),
  ('PyPDF2._version',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_version.py',
   'PYMODULE'),
  ('PyPDF2._reader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_reader.py',
   'PYMODULE'),
  ('PyPDF2._page',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_page.py',
   'PYMODULE'),
  ('PyPDF2._cmap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_cmap.py',
   'PYMODULE'),
  ('PyPDF2._merger',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_merger.py',
   'PYMODULE'),
  ('PyPDF2._encryption',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PyPDF2\\_encryption.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\imp.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Cipher.ARC4',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\ARC4.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Random',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kwp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kwp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kw',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kw.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('docx.oxml.ns',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\ns.py',
   'PYMODULE'),
  ('docx.oxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\__init__.py',
   'PYMODULE'),
  ('docx.oxml.text.parfmt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\parfmt.py',
   'PYMODULE'),
  ('docx.oxml.text',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\__init__.py',
   'PYMODULE'),
  ('docx.oxml.xmlchemy',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\xmlchemy.py',
   'PYMODULE'),
  ('docx.enum.base',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\base.py',
   'PYMODULE'),
  ('docx.enum',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\__init__.py',
   'PYMODULE'),
  ('docx.oxml.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\exceptions.py',
   'PYMODULE'),
  ('lxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\difflib.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\cgi.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\doctest.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pydoc.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tty.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\codeop.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\cmd.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('docx.oxml.simpletypes',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\simpletypes.py',
   'PYMODULE'),
  ('docx.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\exceptions.py',
   'PYMODULE'),
  ('docx.oxml.text.paragraph',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\paragraph.py',
   'PYMODULE'),
  ('docx.oxml.text.font',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\font.py',
   'PYMODULE'),
  ('docx.enum.dml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\dml.py',
   'PYMODULE'),
  ('docx.oxml.table',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\table.py',
   'PYMODULE'),
  ('docx.enum.table',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\table.py',
   'PYMODULE'),
  ('docx.oxml.styles',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\styles.py',
   'PYMODULE'),
  ('docx.enum.style',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\style.py',
   'PYMODULE'),
  ('docx.oxml.settings',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\settings.py',
   'PYMODULE'),
  ('docx.oxml.section',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\section.py',
   'PYMODULE'),
  ('docx.enum.section',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\section.py',
   'PYMODULE'),
  ('docx.oxml.numbering',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\numbering.py',
   'PYMODULE'),
  ('docx.oxml.document',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\document.py',
   'PYMODULE'),
  ('docx.oxml.coreprops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\coreprops.py',
   'PYMODULE'),
  ('docx.oxml.comments',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\comments.py',
   'PYMODULE'),
  ('docx.oxml.text.run',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\run.py',
   'PYMODULE'),
  ('docx.oxml.text.pagebreak',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\pagebreak.py',
   'PYMODULE'),
  ('docx.oxml.text.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\text\\hyperlink.py',
   'PYMODULE'),
  ('docx.oxml.shared',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\shared.py',
   'PYMODULE'),
  ('docx.oxml.shape',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\shape.py',
   'PYMODULE'),
  ('docx.oxml.parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\parser.py',
   'PYMODULE'),
  ('docx.oxml.drawing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\oxml\\drawing.py',
   'PYMODULE'),
  ('docx.enum.text',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\text.py',
   'PYMODULE'),
  ('docx.shared',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\shared.py',
   'PYMODULE'),
  ('docx.parts.story',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\story.py',
   'PYMODULE'),
  ('docx.styles.style',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\styles\\style.py',
   'PYMODULE'),
  ('docx.text.parfmt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\parfmt.py',
   'PYMODULE'),
  ('docx.text',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\__init__.py',
   'PYMODULE'),
  ('docx.text.tabstops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\tabstops.py',
   'PYMODULE'),
  ('docx.text.font',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\font.py',
   'PYMODULE'),
  ('docx.dml.color',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\dml\\color.py',
   'PYMODULE'),
  ('docx.dml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\dml\\__init__.py',
   'PYMODULE'),
  ('docx.styles',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\styles\\__init__.py',
   'PYMODULE'),
  ('docx.parts.document',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\document.py',
   'PYMODULE'),
  ('docx.settings',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\settings.py',
   'PYMODULE'),
  ('docx.opc.coreprops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\coreprops.py',
   'PYMODULE'),
  ('docx.opc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\__init__.py',
   'PYMODULE'),
  ('docx.comments',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\comments.py',
   'PYMODULE'),
  ('docx.text.paragraph',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\paragraph.py',
   'PYMODULE'),
  ('docx.text.run',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\run.py',
   'PYMODULE'),
  ('docx.drawing',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\drawing\\__init__.py',
   'PYMODULE'),
  ('docx.text.pagebreak',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\pagebreak.py',
   'PYMODULE'),
  ('docx.text.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\text\\hyperlink.py',
   'PYMODULE'),
  ('docx.blkcntnr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\blkcntnr.py',
   'PYMODULE'),
  ('docx.table',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\table.py',
   'PYMODULE'),
  ('docx.shape',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\shape.py',
   'PYMODULE'),
  ('docx.enum.shape',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\enum\\shape.py',
   'PYMODULE'),
  ('docx.parts.styles',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\styles.py',
   'PYMODULE'),
  ('docx.opc.package',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\package.py',
   'PYMODULE'),
  ('docx.opc.rel',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\rel.py',
   'PYMODULE'),
  ('docx.opc.oxml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\oxml.py',
   'PYMODULE'),
  ('docx.opc.pkgwriter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\pkgwriter.py',
   'PYMODULE'),
  ('docx.opc.spec',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\spec.py',
   'PYMODULE'),
  ('docx.opc.shared',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\shared.py',
   'PYMODULE'),
  ('docx.opc.phys_pkg',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\phys_pkg.py',
   'PYMODULE'),
  ('docx.opc.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\exceptions.py',
   'PYMODULE'),
  ('docx.opc.pkgreader',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\pkgreader.py',
   'PYMODULE'),
  ('docx.opc.parts.coreprops',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\parts\\coreprops.py',
   'PYMODULE'),
  ('docx.opc.parts',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\parts\\__init__.py',
   'PYMODULE'),
  ('docx.styles.styles',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\styles\\styles.py',
   'PYMODULE'),
  ('docx.styles.latent',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\styles\\latent.py',
   'PYMODULE'),
  ('docx.opc.packuri',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\packuri.py',
   'PYMODULE'),
  ('docx.parts.settings',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\settings.py',
   'PYMODULE'),
  ('docx.package',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\package.py',
   'PYMODULE'),
  ('docx.parts.image',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\image.py',
   'PYMODULE'),
  ('docx.parts.numbering',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\numbering.py',
   'PYMODULE'),
  ('docx.parts.hdrftr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\hdrftr.py',
   'PYMODULE'),
  ('docx.parts.comments',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\comments.py',
   'PYMODULE'),
  ('docx.document',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\document.py',
   'PYMODULE'),
  ('docx.section',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\section.py',
   'PYMODULE'),
  ('docx.parts',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\parts\\__init__.py',
   'PYMODULE'),
  ('docx.image.image',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\image.py',
   'PYMODULE'),
  ('docx.image',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\__init__.py',
   'PYMODULE'),
  ('docx.image.tiff',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\tiff.py',
   'PYMODULE'),
  ('docx.image.helpers',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\helpers.py',
   'PYMODULE'),
  ('docx.image.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\constants.py',
   'PYMODULE'),
  ('docx.image.png',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\png.py',
   'PYMODULE'),
  ('docx.image.jpeg',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\jpeg.py',
   'PYMODULE'),
  ('docx.image.gif',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\gif.py',
   'PYMODULE'),
  ('docx.image.bmp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\bmp.py',
   'PYMODULE'),
  ('docx.image.exceptions',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\image\\exceptions.py',
   'PYMODULE'),
  ('docx.opc.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\constants.py',
   'PYMODULE'),
  ('docx.opc.part',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\opc\\part.py',
   'PYMODULE'),
  ('docx.types',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\types.py',
   'PYMODULE'),
  ('docx',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\__init__.py',
   'PYMODULE'),
  ('docx.api',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\api.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_strptime.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\tkinter\\constants.py',
   'PYMODULE')],
 [('python311.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\python311.dll',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve25519.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve448.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\etree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\_elementpath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\sax.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\objectify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\diff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\html\\_difflib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\builder.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\zlib.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('libexpat.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Program Files\\Git\\mingw64\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Program Files\\Git\\mingw64\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ffi.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\fontTable.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\fontTable.xml',
   'DATA'),
  ('docx\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\py.typed',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\theme\\theme1.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\theme\\theme1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\styles.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\styles.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\_rels\\document.xml.rels',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\_rels\\document.xml.rels',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\webSettings.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\webSettings.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\core.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\core.xml',
   'DATA'),
  ('docx\\templates\\default-styles.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-styles.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\stylesWithEffects.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\stylesWithEffects.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\settings.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\settings.xml',
   'DATA'),
  ('docx\\templates\\default.docx',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default.docx',
   'DATA'),
  ('docx\\templates\\default-header.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-header.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\numbering.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\numbering.xml',
   'DATA'),
  ('docx\\templates\\default-settings.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-settings.xml',
   'DATA'),
  ('docx\\templates\\default-footer.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-footer.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\itemProps1.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\itemProps1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\[Content_Types].xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\[Content_Types].xml',
   'DATA'),
  ('docx\\templates\\default-comments.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-comments.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\item1.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\item1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\app.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\app.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\_rels\\item1.xml.rels',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\_rels\\item1.xml.rels',
   'DATA'),
  ('docx\\templates\\default-docx-template\\_rels\\.rels',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\_rels\\.rels',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\document.xml',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\document.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\thumbnail.jpeg',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\thumbnail.jpeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.7.tm',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tcl8\\8.5\\tcltest-2.5.7.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:/Users/<USER>/Desktop/自动分类/.conda/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\build\\文件自动分类工具2.0\\base_library.zip',
   'DATA')],
 [('io',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\io.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('functools',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\functools.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\ntpath.py',
   'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\operator.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\genericpath.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('enum',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\enum.py',
   'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\codecs.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\keyword.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\stat.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\warnings.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\reprlib.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\locale.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\heapq.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\types.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\posixpath.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\linecache.py',
   'PYMODULE'),
  ('abc',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\abc.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\weakref.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\copyreg.py',
   'PYMODULE'),
  ('re._parser',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('traceback',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\traceback.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\Desktop\\自动分类\\.conda\\Lib\\os.py',
   'PYMODULE')])

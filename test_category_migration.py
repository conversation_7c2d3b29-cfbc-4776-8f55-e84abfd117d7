#!/usr/bin/env python3
"""
测试分类规则管理功能迁移
验证从file_sorter_gui_new.py迁移到gui_new2.0.py的功能是否正常工作
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_category_config_dialog():
    """测试分类配置对话框"""
    print("测试分类配置对话框...")
    
    try:
        # 导入gui_new2.0.py中的类
        import importlib.util
        spec = importlib.util.spec_from_file_location("gui_new2", "gui_new2.0.py")
        gui_new2 = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gui_new2)

        CategoryConfigDialog = gui_new2.CategoryConfigDialog
        ConfigManager = gui_new2.ConfigManager
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        config_manager = ConfigManager()
        existing_names = [c['name'] for c in config_manager.get_categories()]
        
        # 测试添加新分类对话框
        print("✅ 成功导入CategoryConfigDialog")
        print(f"✅ 现有分类数量: {len(existing_names)}")
        
        # 创建对话框（但不显示）
        dialog = CategoryConfigDialog(root, existing_names=existing_names)
        print("✅ 成功创建CategoryConfigDialog实例")
        
        # 测试结果获取方法
        result = dialog.get_result()
        print(f"✅ get_result()方法正常: {result}")
        
        dialog.dialog.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ CategoryConfigDialog测试失败: {str(e)}")
        return False

def test_rule_manager_window():
    """测试规则管理器窗口"""
    print("\n测试规则管理器窗口...")
    
    try:
        # 导入gui_new2.0.py中的类
        import importlib.util
        spec = importlib.util.spec_from_file_location("gui_new2", "gui_new2.0.py")
        gui_new2 = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gui_new2)

        RuleManagerWindow = gui_new2.RuleManagerWindow
        ConfigManager = gui_new2.ConfigManager
        ToastNotification = gui_new2.ToastNotification
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        config_manager = ConfigManager()
        
        # 创建Toast通知实例
        toast = ToastNotification(root)
        
        # 创建规则管理器窗口（但不显示）
        rule_manager = RuleManagerWindow(root, config_manager, toast)
        print("✅ 成功创建RuleManagerWindow实例")
        
        # 测试加载规则方法
        rule_manager.load_rules()
        print("✅ load_rules()方法正常执行")
        
        # 检查树视图是否有数据
        items = rule_manager.category_tree.get_children()
        print(f"✅ 分类规则数量: {len(items)}")
        
        rule_manager.window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ RuleManagerWindow测试失败: {str(e)}")
        return False

def test_config_manager_integration():
    """测试配置管理器集成"""
    print("\n测试配置管理器集成...")
    
    try:
        # 导入gui_new2.0.py中的类
        import importlib.util
        spec = importlib.util.spec_from_file_location("gui_new2", "gui_new2.0.py")
        gui_new2 = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gui_new2)

        ConfigManager = gui_new2.ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试获取分类
        categories = config_manager.get_categories()
        print(f"✅ 获取分类成功，数量: {len(categories)}")
        
        # 测试获取特定分类
        if categories:
            first_category = categories[0]
            category_name = first_category['name']
            category_data = config_manager.get_category_by_name(category_name)
            print(f"✅ 获取特定分类成功: {category_name}")
            print(f"   关键词数量: {len(category_data.get('keywords', []))}")
            print(f"   匹配类型: {category_data.get('match_type', 'unknown')}")
            print(f"   优先级: {category_data.get('priority', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ ConfigManager集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("分类规则管理功能迁移测试")
    print("=" * 60)
    
    tests = [
        test_config_manager_integration,
        test_category_config_dialog,
        test_rule_manager_window,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！分类规则管理功能迁移成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题切换功能测试脚本
测试GUI 2.0的主题切换和字体大小调整功能
"""

import subprocess
import sys
import os

def test_theme_switching():
    """测试主题切换功能"""
    print("🎨 主题切换功能测试")
    print("=" * 50)

    print("🚀 启动GUI 2.0进行主题测试...")
    print("\n📋 测试步骤:")
    print("1. 点击右上角'设置'按钮")
    print("2. 在'界面设置'标签页中测试以下功能:")
    print("   - 选择不同的颜色方案:")
    print("     • 默认蓝色")
    print("     • 商务灰色")
    print("     • 护眼绿色")
    print("     • 温暖橙色")
    print("   - 选择不同的字体大小:")
    print("     • 小")
    print("     • 中等")
    print("     • 大")
    print("     • 特大")
    print("3. 点击'应用设置'按钮")
    print("4. 观察界面是否立即更新")
    print("5. 检查所有组件的颜色和字体是否正确应用")
    print("\n✅ 预期结果:")
    print("- 选择不同颜色方案后，界面颜色应立即改变")
    print("- 选择不同字体大小后，所有文字大小应立即调整")
    print("- 设置应该被保存，重启应用后保持选择的主题")

    # 启动GUI应用
    try:
        subprocess.run([sys.executable, "gui_new2.0.py"], cwd=os.getcwd())
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")

if __name__ == "__main__":
    print("🚀 GUI 2.0 主题系统测试")
    print("=" * 50)

    # 启动主题切换测试
    test_theme_switching()

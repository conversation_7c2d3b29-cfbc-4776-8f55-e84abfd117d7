"""
配置管理模块
负责处理分类规则的加载、保存、验证和管理
"""

import json
import os
import logging
from typing import List, Dict, Any, Optional
from .exceptions import ConfigError, ValidationError


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = 'categories_config.json'):
        self.config_file = config_file
        self.categories_config = []
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.categories_config = json.load(f)
                self._validate_config()
                self._fix_config_issues()
                logging.info(f"配置文件加载成功: {self.config_file}")
            except json.JSONDecodeError as e:
                logging.error(f"配置文件JSON格式错误: {e}")
                raise ConfigError(f"配置文件格式错误: {str(e)}")
            except Exception as e:
                logging.error(f"加载配置文件失败: {e}")
                raise ConfigError(f"加载配置文件失败: {str(e)}")
        else:
            self._load_default_config()
    
    def _load_default_config(self) -> None:
        """加载默认配置"""
        self.categories_config = [
            {
                'name': '美盟方向',
                'keywords': ['美','日','韩','澳','拜登','特朗普'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '中东方向',
                'keywords': ['伊朗', '巴以','胡赛','叙','伊拉克','中东'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '台湾方向',
                'keywords': ['台', '对台','美台','台湾'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '半岛方向',
                'keywords': ['朝鲜','朝','韩国'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '俄乌方向',
                'keywords': ['俄','俄乌','俄罗斯','乌克兰'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '南亚方向',
                'keywords': ['缅','越','柬','缅甸','越南','柬埔寨'],
                'match_type': 'contains',
                'priority': 1
            }, 
            {
                'name': '反恐维稳',
                'keywords': ['维和','恐怖','维稳'],
                'match_type': 'contains',
                'priority': 1
            },         
            {
                'name': '涉我方向',
                'keywords': [],
                'match_type': 'default',
                'priority': 1
            }
        ]
        self.save_config()
        logging.info("已加载默认配置")
    
    def _validate_config(self) -> None:
        """验证配置的有效性"""
        if not isinstance(self.categories_config, list):
            raise ValidationError("配置必须是一个列表")
        
        default_count = 0
        names = set()
        
        for config in self.categories_config:
            if not isinstance(config, dict):
                raise ValidationError("每个配置项必须是字典格式")
            
            # 检查必需字段
            required_fields = ['name', 'keywords', 'match_type', 'priority']
            for field in required_fields:
                if field not in config:
                    raise ValidationError(f"配置项缺少必需字段: {field}")
            
            # 检查名称唯一性
            if config['name'] in names:
                logging.warning(f"发现重复的分类名称: {config['name']}")
            names.add(config['name'])
            
            # 检查默认分类数量
            if config['match_type'] == 'default':
                default_count += 1
        
        if default_count > 1:
            logging.warning("发现多个默认分类，将只保留第一个")
    
    def _fix_config_issues(self) -> None:
        """修复配置中的问题"""
        # 移除重复的分类
        seen_names = set()
        fixed_config = []
        
        for config in self.categories_config:
            if config['name'] not in seen_names:
                seen_names.add(config['name'])
                fixed_config.append(config)
            else:
                logging.info(f"移除重复的分类: {config['name']}")
        
        # 确保只有一个默认分类
        default_found = False
        for config in fixed_config:
            if config['match_type'] == 'default':
                if default_found:
                    config['match_type'] = 'contains'
                    logging.info(f"将重复的默认分类 '{config['name']}' 改为包含匹配")
                else:
                    default_found = True
        
        self.categories_config = fixed_config
        
        # 如果修复了问题，保存配置
        if len(self.categories_config) != len(seen_names):
            self.save_config()
    
    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.categories_config, f, ensure_ascii=False, indent=4)
            logging.info("配置文件保存成功")
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
            raise ConfigError(f"保存配置文件失败: {str(e)}")
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """获取所有分类配置"""
        return self.categories_config.copy()
    
    def add_category(self, name: str, keywords: List[str], match_type: str = 'contains', priority: int = 1) -> None:
        """添加新分类"""
        # 验证参数
        if not name.strip():
            raise ValidationError("分类名称不能为空")
        
        if any(c['name'] == name for c in self.categories_config):
            raise ValidationError(f"分类名称 '{name}' 已存在")
        
        if match_type == 'default' and any(c['match_type'] == 'default' for c in self.categories_config):
            raise ValidationError("只能有一个默认分类")
        
        # 添加新分类
        new_category = {
            'name': name,
            'keywords': keywords,
            'match_type': match_type,
            'priority': priority
        }
        
        self.categories_config.append(new_category)
        self.save_config()
        logging.info(f"已添加新分类: {name}")
    
    def update_category(self, old_name: str, name: str, keywords: List[str], match_type: str, priority: int) -> None:
        """更新分类"""
        category = self.get_category_by_name(old_name)
        if not category:
            raise ValidationError(f"分类 '{old_name}' 不存在")
        
        # 检查名称冲突（排除当前分类）
        if name != old_name and any(c['name'] == name for c in self.categories_config):
            raise ValidationError(f"分类名称 '{name}' 已存在")
        
        # 检查默认分类冲突
        if match_type == 'default' and any(c['match_type'] == 'default' and c['name'] != old_name for c in self.categories_config):
            raise ValidationError("只能有一个默认分类")
        
        # 更新分类
        index = self.categories_config.index(category)
        self.categories_config[index] = {
            'name': name,
            'keywords': keywords,
            'match_type': match_type,
            'priority': priority
        }
        
        self.save_config()
        logging.info(f"已更新分类: {old_name} -> {name}")
    
    def delete_category(self, name: str) -> None:
        """删除分类"""
        category = self.get_category_by_name(name)
        if not category:
            raise ValidationError(f"分类 '{name}' 不存在")
        
        if category['match_type'] == 'default':
            raise ValidationError("不能删除默认分类")
        
        self.categories_config.remove(category)
        self.save_config()
        logging.info(f"已删除分类: {name}")
    
    def get_category_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取分类配置"""
        return next((c for c in self.categories_config if c['name'] == name), None)
    
    def get_category_for_filename(self, filename: str) -> str:
        """根据文件名获取分类"""
        matched_categories = []
        default_category = None
        
        for config in self.categories_config:
            if config['match_type'] == 'contains' and any(keyword in filename for keyword in config['keywords'] if keyword):
                matched_categories.append(config)
            elif config['match_type'] == 'startswith' and any(filename.startswith(keyword) for keyword in config['keywords'] if keyword):
                matched_categories.append(config)
            elif config['match_type'] == 'default':
                default_category = config['name']
        
        if matched_categories:
            # 按优先级排序，取最高优先级的分类
            matched_categories.sort(key=lambda x: x.get('priority', 0), reverse=True)
            return matched_categories[0]['name']
        
        return default_category or "未分类"
    
    def export_config(self, export_path: str) -> None:
        """导出配置到指定路径"""
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.categories_config, f, ensure_ascii=False, indent=4)
            logging.info(f"配置已导出到: {export_path}")
        except Exception as e:
            raise ConfigError(f"导出配置失败: {str(e)}")
    
    def import_config(self, import_path: str) -> None:
        """从指定路径导入配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 临时保存当前配置
            backup_config = self.categories_config.copy()
            
            try:
                self.categories_config = imported_config
                self._validate_config()
                self._fix_config_issues()
                self.save_config()
                logging.info(f"配置已从 {import_path} 导入")
            except Exception as e:
                # 恢复备份配置
                self.categories_config = backup_config
                raise ConfigError(f"导入的配置无效: {str(e)}")
                
        except Exception as e:
            raise ConfigError(f"导入配置失败: {str(e)}")

    def get_category_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取分类配置"""
        for category in self.categories_config:
            if category['name'] == name:
                return category.copy()
        return None

    def add_category(self, name: str, keywords: List[str], match_type: str = 'contains', priority: int = 1):
        """添加新分类"""
        if any(c['name'] == name for c in self.categories_config):
            raise ConfigError(f"分类 '{name}' 已存在")

        new_category = {
            'name': name,
            'keywords': keywords,
            'match_type': match_type,
            'priority': priority
        }

        self.categories_config.append(new_category)
        self.save_config()
        logging.info(f"已添加分类: {name}")

    def update_category(self, old_name: str, new_name: str, keywords: List[str],
                       match_type: str = 'contains', priority: int = 1):
        """更新分类配置"""
        category = self.get_category_by_name(old_name)
        if not category:
            raise ConfigError(f"找不到分类 '{old_name}'")

        # 检查新名称是否冲突（除了自己）
        if new_name != old_name and any(c['name'] == new_name for c in self.categories_config):
            raise ConfigError(f"分类 '{new_name}' 已存在")

        # 更新分类
        for i, category in enumerate(self.categories_config):
            if category['name'] == old_name:
                self.categories_config[i] = {
                    'name': new_name,
                    'keywords': keywords,
                    'match_type': match_type,
                    'priority': priority
                }
                break

        self.save_config()
        logging.info(f"已更新分类: {old_name} -> {new_name}")

    def delete_category(self, name: str):
        """删除分类"""
        original_count = len(self.categories_config)
        self.categories_config = [c for c in self.categories_config if c['name'] != name]

        if len(self.categories_config) == original_count:
            raise ConfigError(f"找不到分类 '{name}'")

        self.save_config()
        logging.info(f"已删除分类: {name}")

    def adjust_category_priority(self, name: str, adjustment: int):
        """调整分类优先级"""
        category = self.get_category_by_name(name)
        if not category:
            raise ConfigError(f"找不到分类 '{name}'")

        for i, category in enumerate(self.categories_config):
            if category['name'] == name:
                new_priority = max(1, category['priority'] + adjustment)
                self.categories_config[i]['priority'] = new_priority
                break

        self.save_config()
        logging.info(f"已调整分类 '{name}' 优先级: {adjustment}")

    def export_config(self, file_path: str):
        """导出配置文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.categories_config, f, ensure_ascii=False, indent=4)

            logging.info(f"已导出配置文件: {file_path}")

        except Exception as e:
            raise ConfigError(f"导出配置文件失败: {str(e)}")

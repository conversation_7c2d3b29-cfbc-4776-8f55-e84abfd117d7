"""
文档生成模块
负责生成Word汇总文档
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Tuple

import docx
from docx.shared import Cm, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

from .exceptions import DocumentGenerationError
from .config_manager import ConfigManager


class DocumentGenerator:
    """文档生成器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
    
    def generate_summary_document(self, category_files: Dict[str, List[Tuple[str, str]]],
                                output_path: str = None) -> str:
        """生成汇总文档"""
        try:
            # 初始化文档
            doc = self._init_document()

            # 添加目录
            toc_para = self._add_table_of_contents(doc)
            doc.add_page_break()

            # 对所有分类的内容进行去重处理
            deduplicated_category_files = self._deduplicate_content(category_files)

            # 按分类优先级排序并添加内容
            categories = self.config_manager.get_categories()
            category_priority = {config['name']: config.get('priority', 0) for config in categories}

            for category in sorted(deduplicated_category_files.keys(), key=lambda x: category_priority.get(x, 0), reverse=True):
                if deduplicated_category_files[category]:  # 只添加有内容的分类
                    self._add_category_section(doc, category, deduplicated_category_files[category])
            
            # 将目录移动到文档开头
            doc._body._element.insert(0, toc_para._element)
            
            # 移除原位置的空段落
            self._remove_empty_paragraphs(doc)
            
            # 确定输出路径
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"文章汇总_{timestamp}.docx"
            
            # 保存文档
            doc.save(output_path)
            logging.info(f"汇总文档已生成: {output_path}")
            
            return output_path
            
        except Exception as e:
            logging.error(f"生成汇总文档失败: {e}")
            raise DocumentGenerationError(f"生成汇总文档失败: {str(e)}")

    def _deduplicate_content(self, category_files: Dict[str, List[Tuple[str, str]]]) -> Dict[str, List[Tuple[str, str]]]:
        """对内容进行去重处理，相同标题的内容不重复汇总"""
        seen_titles = set()  # 记录已经处理过的标题
        seen_content_hashes = set()  # 记录已经处理过的内容哈希
        deduplicated_files = {}
        duplicate_count = 0

        # 按分类优先级处理，确保高优先级的内容优先保留
        categories = self.config_manager.get_categories()
        category_priority = {config['name']: config.get('priority', 0) for config in categories}

        # 按优先级排序分类
        sorted_categories = sorted(category_files.keys(),
                                 key=lambda x: category_priority.get(x, 0),
                                 reverse=True)

        for category in sorted_categories:
            files_content = category_files[category]
            deduplicated_files[category] = []

            for title, content in files_content:
                # 标准化标题（去除空格、标点符号等）
                normalized_title = self._normalize_title(title)

                # 计算内容哈希（用于检测内容相似度）
                content_hash = self._calculate_content_hash(content)

                # 检查是否重复
                is_duplicate = False
                duplicate_reason = ""

                # 1. 检查标题重复
                if normalized_title in seen_titles:
                    is_duplicate = True
                    duplicate_reason = f"标题重复: '{title}'"

                # 2. 检查内容重复（即使标题不同，内容相同也视为重复）
                elif content_hash in seen_content_hashes:
                    is_duplicate = True
                    duplicate_reason = f"内容重复: '{title}'"

                # 3. 检查内容相似度（可选，用于检测高度相似的内容）
                elif self._is_content_similar(content, seen_content_hashes):
                    is_duplicate = True
                    duplicate_reason = f"内容高度相似: '{title}'"

                if is_duplicate:
                    duplicate_count += 1
                    logging.info(f"跳过重复内容 - {duplicate_reason} (分类: {category})")
                else:
                    # 添加到结果中
                    deduplicated_files[category].append((title, content))
                    seen_titles.add(normalized_title)
                    seen_content_hashes.add(content_hash)
                    logging.debug(f"添加内容: '{title}' (分类: {category})")

        logging.info(f"内容去重完成，跳过 {duplicate_count} 个重复项")
        return deduplicated_files

    def _normalize_title(self, title: str) -> str:
        """标准化标题，用于重复检测"""
        import re

        # 去除文件扩展名
        title = re.sub(r'\.(txt|doc|docx|pdf)$', '', title, flags=re.IGNORECASE)

        # 去除所有空白字符和特殊字符
        title = re.sub(r'\s+', '', title)

        # 去除所有标点符号和特殊字符，只保留中文、英文字母、数字
        # 注意：\w包含下划线，所以需要单独处理
        title = re.sub(r'[^\u4e00-\u9fffA-Za-z0-9]', '', title)

        # 转换为小写（对于英文部分）
        title = title.lower()

        return title

    def _calculate_content_hash(self, content: str) -> str:
        """计算内容哈希值"""
        import hashlib
        import re

        # 标准化内容处理
        # 1. 去除所有空白字符（空格、制表符、换行符等）
        normalized_content = re.sub(r'\s+', '', content)

        # 2. 去除常见的标点符号
        # 分步骤去除不同类型的标点符号
        normalized_content = re.sub(r'[，。！？；：""''（）【】《》]', '', normalized_content)  # 中文标点
        normalized_content = re.sub(r'[\.,:;!?\(\)\[\]<>"\']', '', normalized_content)  # 英文标点

        # 3. 转换为小写（对英文部分）
        normalized_content = normalized_content.lower()

        # 计算MD5哈希
        return hashlib.md5(normalized_content.encode('utf-8')).hexdigest()

    def _is_content_similar(self, content: str, seen_hashes: set, similarity_threshold: float = 0.8) -> bool:
        """检查内容是否与已有内容高度相似"""
        # 简单的相似度检测：基于内容长度和关键词
        content_words = set(content.split())
        content_length = len(content)

        # 如果内容太短，不进行相似度检测
        if content_length < 100:
            return False

        # 这里可以实现更复杂的相似度算法
        # 目前使用简单的长度和哈希前缀比较
        content_hash = self._calculate_content_hash(content)
        content_prefix = content_hash[:8]  # 使用哈希前8位进行快速比较

        for seen_hash in seen_hashes:
            if seen_hash[:8] == content_prefix:
                return True

        return False

    def _init_document(self) -> docx.Document:
        """初始化文档"""
        doc = docx.Document()
        
        # 设置页边距
        sections = doc.sections
        for section in sections:
            section.top_margin = Cm(3.7)
            section.bottom_margin = Cm(3.5)
            section.left_margin = Cm(2.8)
            section.right_margin = Cm(2.6)
            
            # 添加居中页码
            self._add_page_numbers(section)
        
        return doc
    
    def _add_page_numbers(self, section) -> None:
        """添加页码"""
        try:
            footer = section.footer
            para = footer.paragraphs[0]
            para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = para.add_run()
            
            # 添加页码字段
            fld_char = OxmlElement('w:fldChar')
            fld_char.set(qn('w:fldCharType'), 'begin')
            run._r.append(fld_char)
            
            instr_text = OxmlElement('w:instrText')
            instr_text.text = "PAGE"
            run._r.append(instr_text)
            
            fld_char = OxmlElement('w:fldChar')
            fld_char.set(qn('w:fldCharType'), 'end')
            run._r.append(fld_char)
        except Exception as e:
            logging.warning(f"添加页码失败: {e}")
    
    def _add_table_of_contents(self, doc: docx.Document) -> docx.text.paragraph.Paragraph:
        """添加目录"""
        para = doc.add_paragraph()
        run = para.add_run()
        
        try:
            # 添加目录字段代码
            fld_char_begin = OxmlElement('w:fldChar')
            fld_char_begin.set(qn('w:fldCharType'), 'begin')
            run._r.append(fld_char_begin)
            
            instr_text = OxmlElement('w:instrText')
            instr_text.text = r'TOC \o "1-2" \h \z \u'  # 显示1-2级标题，带超链接
            run._r.append(instr_text)
            
            fld_char_end = OxmlElement('w:fldChar')
            fld_char_end.set(qn('w:fldCharType'), 'end')
            run._r.append(fld_char_end)
            
        except Exception as e:
            logging.warning(f"添加目录字段失败: {e}")
            # 如果添加目录字段失败，添加简单的目录标题
            para.clear()
            run = para.add_run("目录")
            run.font.size = Pt(16)
            run.font.bold = True
            para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        return para
    
    def _add_category_section(self, doc: docx.Document, category: str, 
                            files_content: List[Tuple[str, str]]) -> None:
        """添加分类章节"""
        # 添加一级标题（分类名称）
        self._add_title(doc, category, level=1)
        
        # 添加该分类下的所有文件内容
        for title, content in files_content:
            if content and content.strip():  # 只添加有内容的文件
                self._add_title(doc, title, level=2)
                self._add_content(doc, content)
    
    def _add_title(self, doc: docx.Document, title: str, level: int) -> None:
        """添加标题"""
        title_para = doc.add_paragraph(title, style=f'Heading {level}')
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        title_run = title_para.runs[0]
        title_run.font.name = '方正小标宋简体'
        title_run._element.rPr.rFonts.set(qn('w:eastAsia'), '方正小标宋简体')
        title_run.font.color.rgb = RGBColor(0, 0, 0)
        
        if level == 1:
            title_run.font.size = Pt(22)  # 一级标题字号
        else:
            title_run.font.size = Pt(16)  # 二级标题字号
            
        title_run.font.bold = True
        doc.add_paragraph()  # 添加空行
    
    def _add_content(self, doc: docx.Document, content: str) -> None:
        """添加正文内容"""
        content_para = doc.add_paragraph()
        content_run = content_para.add_run(content)
        content_run.font.name = '仿宋GB2312'
        content_run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋GB2312')
        content_run.font.size = Pt(16)  # 三号字对应16pt
        content_run.font.color.rgb = RGBColor(0, 0, 0)
        
        # 设置首行缩进2个字符
        content_para.paragraph_format.first_line_indent = Pt(32)
        # 设置行距28.95磅
        content_para.paragraph_format.line_spacing = Pt(28.95)
        doc.add_page_break()  # 分页
    
    def _remove_empty_paragraphs(self, doc: docx.Document) -> None:
        """移除空段落"""
        try:
            paragraphs_to_remove = []
            for para in doc.paragraphs:
                if not para.text.strip():
                    paragraphs_to_remove.append(para)
            
            for para in paragraphs_to_remove[:1]:  # 只移除第一个空段落
                p = para._element
                p.getparent().remove(p)
                break
        except Exception as e:
            logging.warning(f"移除空段落失败: {e}")
    
    def generate_statistics_report(self, processed_files: List[Dict[str, Any]], 
                                 output_path: str = None) -> str:
        """生成统计报告"""
        try:
            doc = self._init_document()
            
            # 添加报告标题
            title_para = doc.add_paragraph("文件分类统计报告")
            title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title_run = title_para.runs[0]
            title_run.font.size = Pt(24)
            title_run.font.bold = True
            doc.add_paragraph()
            
            # 生成统计数据
            stats = self._calculate_statistics(processed_files)
            
            # 添加统计内容
            self._add_statistics_content(doc, stats)
            
            # 确定输出路径
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"分类统计报告_{timestamp}.docx"
            
            doc.save(output_path)
            logging.info(f"统计报告已生成: {output_path}")
            
            return output_path
            
        except Exception as e:
            logging.error(f"生成统计报告失败: {e}")
            raise DocumentGenerationError(f"生成统计报告失败: {str(e)}")
    
    def _calculate_statistics(self, processed_files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算统计数据"""
        total_files = len(processed_files)
        successful_files = len([f for f in processed_files if f.get('status') == '已完成'])
        failed_files = total_files - successful_files
        
        # 按分类统计
        category_stats = {}
        for file_info in processed_files:
            category = file_info.get('category', '未知')
            if category not in category_stats:
                category_stats[category] = {'count': 0, 'size': 0}
            category_stats[category]['count'] += 1
            category_stats[category]['size'] += file_info.get('size', 0)
        
        # 按状态统计
        status_stats = {}
        for file_info in processed_files:
            status = file_info.get('status', '未知')
            status_stats[status] = status_stats.get(status, 0) + 1
        
        return {
            'total_files': total_files,
            'successful_files': successful_files,
            'failed_files': failed_files,
            'success_rate': (successful_files / total_files * 100) if total_files > 0 else 0,
            'category_stats': category_stats,
            'status_stats': status_stats,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def _add_statistics_content(self, doc: docx.Document, stats: Dict[str, Any]) -> None:
        """添加统计内容"""
        # 总体统计
        doc.add_paragraph(f"生成时间: {stats['timestamp']}")
        doc.add_paragraph(f"总文件数: {stats['total_files']}")
        doc.add_paragraph(f"成功处理: {stats['successful_files']}")
        doc.add_paragraph(f"处理失败: {stats['failed_files']}")
        doc.add_paragraph(f"成功率: {stats['success_rate']:.1f}%")
        doc.add_paragraph()
        
        # 分类统计
        if stats['category_stats']:
            category_title = doc.add_paragraph("分类统计:")
            category_title.runs[0].font.bold = True
            
            for category, data in stats['category_stats'].items():
                size_mb = data['size'] / (1024 * 1024)
                doc.add_paragraph(f"  {category}: {data['count']} 个文件, {size_mb:.1f} MB")
        
        doc.add_paragraph()
        
        # 状态统计
        if stats['status_stats']:
            status_title = doc.add_paragraph("状态统计:")
            status_title.runs[0].font.bold = True
            
            for status, count in stats['status_stats'].items():
                doc.add_paragraph(f"  {status}: {count} 个文件")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI 2.0 功能测试脚本
测试新实现的功能是否正常工作
"""

import tkinter as tk
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_launch():
    """测试GUI启动"""
    try:
        # 导入GUI模块，注意文件名中的点号
        import importlib.util
        spec = importlib.util.spec_from_file_location("gui_new2", "gui_new2.0.py")
        gui_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gui_module)
        FileSorterApp2 = gui_module.FileSorterApp2
        
        print("正在启动GUI 2.0...")
        root = tk.Tk()
        app = FileSorterApp2(root)
        
        print("✅ GUI 2.0 启动成功")
        print("📋 已实现的功能:")
        print("   - ✅ 优化的窗口布局 (1400x900)")
        print("   - ✅ 设置功能窗口")
        print("   - ✅ 分类规则管理")
        print("   - ✅ 规则编辑器")
        print("   - ✅ 增强的Toast通知系统")
        print("   - ✅ 改进的加载指示器")
        print("   - ✅ 优化的空状态显示")
        print("   - ✅ 三栏布局优化 (320px + 主区域 + 350px)")
        
        print("\n🎯 测试说明:")
        print("1. 点击右上角'设置'按钮测试设置功能")
        print("2. 点击右侧面板的'添加规则'、'编辑规则'、'删除规则'按钮")
        print("3. 点击右侧面板的'生成汇总文档'按钮测试文档总结功能")
        print("4. 检查窗口布局是否合理，各面板宽度是否适当")
        print("5. 测试Toast通知是否正常显示")
        print("6. 测试文件分类和内容提取功能")

        print("\n📋 新增功能:")
        print("   - ✅ 文件名匹配规则（从file_sorter_gui移植）")
        print("   - ✅ 文档总结功能（从file_sorter_gui移植）")
        print("   - ✅ 文件内容提取（支持txt、docx、pdf等格式）")
        print("   - ✅ 分类后文件复制到目标目录")
        print("   - ✅ 生成汇总文档按钮")
        
        # 启动GUI
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def test_config_integration():
    """测试配置管理集成"""
    try:
        from core.config_manager import ConfigManager

        print("\n正在测试配置管理集成...")
        config_manager = ConfigManager()

        # 测试基本配置
        categories = config_manager.get_categories()
        print(f"✅ 配置加载成功，当前分类数量: {len(categories)} 个")

        # 测试添加分类
        try:
            config_manager.add_category(
                name="测试分类",
                keywords=["测试", "test"],
                match_type="contains",
                priority=1
            )
            print("✅ 添加分类测试成功")
        except Exception as e:
            if "已存在" in str(e):
                print("✅ 分类已存在，跳过添加")
            else:
                raise e

        # 测试获取分类
        test_category = config_manager.get_category_by_name("测试分类")
        if test_category:
            print("✅ 获取分类测试成功")

        # 测试文件名分类
        test_filename = "测试文档.docx"
        category = config_manager.get_category_for_filename(test_filename)
        print(f"✅ 文件分类测试成功: '{test_filename}' -> '{category}'")

        return True

    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 GUI 2.0 功能测试开始")
    print("=" * 50)
    
    # 测试配置管理
    config_test = test_config_integration()
    
    print("\n" + "=" * 50)
    
    # 测试GUI启动
    if config_test:
        gui_test = test_gui_launch()
        
        if gui_test:
            print("\n🎉 所有测试完成！GUI 2.0 功能正常")
        else:
            print("\n❌ GUI测试失败")
    else:
        print("\n❌ 配置管理测试失败，跳过GUI测试")

if __name__ == "__main__":
    main()

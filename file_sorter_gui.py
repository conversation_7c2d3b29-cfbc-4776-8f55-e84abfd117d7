import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import os
import sys
import threading
import logging
from datetime import datetime
import webbrowser
from tkinter import scrolledtext
import winsound
import platform

# 导入我们的核心模块
from core.file_processor import FileProcessor
from core.config_manager import ConfigManager
from core.document_generator import DocumentGenerator
from core.exceptions import FileSorterError, ConfigError, ProcessingError
from utils.ui_helpers import UIHelpers

# 确保中文显示正常
if platform.system() == 'Windows':
    default_font = ('微软雅黑', 9)
else:
    default_font = ('SimHei', 9)

# 配置日志
logging.basicConfig(
    filename='file_sorter_log.txt',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)

class FileSorterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("文件自动分类工具 v1.0")
        self.root.geometry("1000x600")
        
        # 添加文件格式选择变量
        self.txt_var = tk.BooleanVar(value=True)
        self.doc_var = tk.BooleanVar(value=True)
        self.pdf_var = tk.BooleanVar(value=True)
        
        # 添加Treeview项目ID存储列表
        self.tree_items = []
        self.root.minsize(800, 500)
        
        # 暂时注释掉图标设置，因为app_icon.ico文件不存在
        # self.root.iconbitmap('app_icon.ico')  # 确保有此图标文件或删除此行

        # 设置中文字体
        self.style = ttk.Style()
        self.style.configure('.', font=default_font)
        self.style.configure('TButton', font=default_font)
        self.style.configure('TLabel', font=default_font)
        self.style.configure('TFrame', background='#f0f0f0')

        # 应用状态变量
        self.work_dir = os.path.dirname(os.path.abspath(__file__))
        self.categories_config = []
        self.processed_files = 0
        self.total_files = 0
        self.processing = False
        self.file_list = []
        self.category_files = {}
        self.processed_titles = set()

        # 加载配置
        self.load_config()

        # 创建界面
        self.create_widgets()

        # 绑定快捷键
        self.bind_shortcuts()

        # 首次运行显示向导
        if not os.path.exists('first_run.flag'):
            self.show_welcome_wizard()
            with open('first_run.flag', 'w', encoding='utf-8') as f:
                f.write('已运行')

    def load_config(self):
        """加载分类规则配置"""
        if os.path.exists('categories_config.json'):
            try:
                with open('categories_config.json', 'r', encoding='utf-8') as f:
                    self.categories_config = json.load(f)
            except Exception as e:
                logging.error(f"加载配置文件失败: {e}")
                messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")
                self._load_default_config()
        else:
            self._load_default_config()

    def _load_default_config(self):
        """加载默认分类规则"""
        self.categories_config = [
            {
                'name': '美盟方向',
                'keywords': ['美','日','韩','澳','拜登','特朗普'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '中东方向',
                'keywords': ['伊朗', '巴以','胡赛','叙','伊拉克'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '台湾方向',
                'keywords': ['台', '对台','美台','台湾'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '半岛方向',
                'keywords': ['朝鲜','朝','韩国'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '俄乌方向',
                'keywords': ['俄','俄乌','俄罗斯','乌克兰'],
                'match_type': 'contains',
                'priority': 1
            },
            {
                'name': '南亚方向',
                'keywords': ['缅','越','柬','缅甸','越南','柬埔寨'],
                'match_type': 'contains',
                'priority': 1
            }, 
            {
                'name': '反恐维稳',
                'keywords': ['维和','恐怖','维稳'],
                'match_type': 'contains',
                'priority': 1
            },         
            {
                'name': '涉我方向',
                'keywords': [],
                'match_type': 'default',
                'priority': 1
            }
        ]
        self.save_config()

    def save_config(self):
        """保存分类规则配置"""
        try:
            with open('categories_config.json', 'w', encoding='utf-8') as f:
                json.dump(self.categories_config, f, ensure_ascii=False, indent=4)
            logging.info("分类规则配置已保存")
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
            messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")

    def create_widgets(self):
        """创建GUI界面组件"""
        # 顶部框架 - 标题和工作目录选择
        top_frame = ttk.Frame(self.root, padding="10")
        top_frame.pack(fill=tk.X)

        # 标题
        title_label = ttk.Label(top_frame, text="文件自动分类工具 v1.0", font=('微软雅黑', 16, 'bold'))
        title_label.pack(side=tk.LEFT, padx=10)

        # 文件格式选择
        format_frame = ttk.Frame(top_frame)
        format_frame.pack(side=tk.LEFT, padx=10)
        ttk.Label(format_frame, text="文件格式:").pack(side=tk.LEFT)
        ttk.Checkbutton(format_frame, text=".txt", variable=self.txt_var).pack(side=tk.LEFT)
        ttk.Checkbutton(format_frame, text=".doc/.docx", variable=self.doc_var).pack(side=tk.LEFT)
        ttk.Checkbutton(format_frame, text=".pdf", variable=self.pdf_var).pack(side=tk.LEFT)

        # 工作目录选择
        dir_frame = ttk.Frame(top_frame)
        dir_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=10)

        ttk.Label(dir_frame, text="工作目录:").pack(side=tk.LEFT, padx=5)
        self.dir_var = tk.StringVar(value=self.work_dir)
        dir_entry = ttk.Entry(dir_frame, textvariable=self.dir_var, width=50)
        dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        browse_btn = ttk.Button(dir_frame, text="浏览...", command=self.browse_directory)
        browse_btn.pack(side=tk.LEFT, padx=5)

        refresh_btn = ttk.Button(dir_frame, text="刷新", command=self.refresh_file_list)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧 - 分类规则配置面板
        left_frame = ttk.LabelFrame(main_frame, text="分类规则配置", padding="10")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10), pady=5)

        # 分类规则树状视图
        self.category_tree = ttk.Treeview(left_frame, columns=('name', 'priority'), show='headings', height=15)
        self.category_tree.heading('name', text='分类名称')
        self.category_tree.heading('priority', text='优先级')
        self.category_tree.column('name', width=150)
        self.category_tree.column('priority', width=60, anchor=tk.CENTER)
        self.category_tree.pack(fill=tk.BOTH, expand=True, pady=5)

        # 绑定双击事件查看关键词
        self.category_tree.bind('<Double-1>', self.view_category_keywords)

        # 分类规则按钮
        category_btn_frame = ttk.Frame(left_frame)
        category_btn_frame.pack(fill=tk.X, pady=5)

        add_category_btn = ttk.Button(category_btn_frame, text="添加分类", command=self.add_category)
        add_category_btn.pack(side=tk.LEFT, padx=5)

        edit_category_btn = ttk.Button(category_btn_frame, text="编辑分类", command=self.edit_category)
        edit_category_btn.pack(side=tk.LEFT, padx=5)

        delete_category_btn = ttk.Button(category_btn_frame, text="删除分类", command=self.delete_category)
        delete_category_btn.pack(side=tk.LEFT, padx=5)

        up_priority_btn = ttk.Button(category_btn_frame, text="↑ 优先级", command=self.increase_priority)
        up_priority_btn.pack(side=tk.RIGHT, padx=5)

        down_priority_btn = ttk.Button(category_btn_frame, text="↓ 优先级", command=self.decrease_priority)
        down_priority_btn.pack(side=tk.RIGHT, padx=5)

        # 右侧 - 文件列表区
        right_frame = ttk.LabelFrame(main_frame, text="待分类文件列表", padding="10")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=5)

        # 文件列表树状视图
        self.file_tree = ttk.Treeview(right_frame, columns=('name', 'size', 'category', 'status'), show='headings')
        self.file_tree.heading('name', text='文件名')
        self.file_tree.heading('size', text='大小')
        self.file_tree.heading('category', text='预测分类')
        self.file_tree.heading('status', text='状态')
        self.file_tree.column('name', width=200)
        self.file_tree.column('size', width=80, anchor='e')
        self.file_tree.column('category', width=100)
        self.file_tree.column('status', width=100)
        self.file_tree.pack(fill=tk.BOTH, expand=True, pady=5)

        # 添加右键菜单
        self.file_menu = tk.Menu(self.root, tearoff=0)
        self.file_menu.add_command(label="打开文件", command=self.open_selected_file)
        self.file_menu.add_command(label="查看属性", command=self.view_file_properties)
        self.file_menu.add_command(label="手动分类", command=self.manual_classify)
        self.file_tree.bind('<Button-3>', self.show_file_menu)

        # 底部框架 - 操作按钮和进度条
        bottom_frame = ttk.Frame(self.root, padding="10")
        bottom_frame.pack(fill=tk.X)

        # 进度条
        progress_frame = ttk.Frame(bottom_frame)
        progress_frame.pack(fill=tk.X, expand=True, side=tk.LEFT, padx=10)

        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT, padx=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, length=300, mode='determinate')
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var, foreground='blue')
        status_label.pack(side=tk.LEFT, padx=10)

        # 操作按钮
        btn_frame = ttk.Frame(bottom_frame)
        btn_frame.pack(side=tk.RIGHT)

        start_btn = ttk.Button(btn_frame, text="开始分类", command=self.start_processing)
        start_btn.pack(side=tk.LEFT, padx=5)

        summary_btn = ttk.Button(btn_frame, text="生成汇总", command=self.generate_summary)
        summary_btn.pack(side=tk.LEFT, padx=5)

        exit_btn = ttk.Button(btn_frame, text="退出", command=self.root.quit)
        exit_btn.pack(side=tk.LEFT, padx=5)

        # 初始化分类规则列表
        self.refresh_category_list()
        # 初始化文件列表
        self.refresh_file_list()

    def bind_shortcuts(self):
        """绑定快捷键"""
        self.root.bind('<Control-s>', lambda event: self.start_processing())
        self.root.bind('<Control-g>', lambda event: self.generate_summary())
        self.root.bind('<F5>', lambda event: self.refresh_file_list())
        self.root.bind('<Escape>', lambda event: self.root.quit())

    def show_welcome_wizard(self):
        """显示欢迎向导"""
        wizard = tk.Toplevel(self.root)
        wizard.title("欢迎使用文件自动分类工具")
        wizard.geometry("600x400")
        wizard.resizable(False, False)
        wizard.transient(self.root)
        wizard.grab_set()

        # 创建向导内容
        notebook = ttk.Notebook(wizard)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 页面1: 欢迎
        page1 = ttk.Frame(notebook)
        notebook.add(page1, text="欢迎")

        ttk.Label(page1, text="欢迎使用文件自动分类工具!本工具可以帮助您根据关键词自动分类文档文件，并生成汇总报告。",
                 font=default_font, wraplength=500, justify=tk.LEFT).pack(pady=50)

        # 页面2: 使用说明
        page2 = ttk.Frame(notebook)
        notebook.add(page2, text="使用说明")

        text = scrolledtext.ScrolledText(page2, wrap=tk.WORD, width=60, height=15, font=default_font)
        text.pack(padx=10, pady=10)
        text.insert(tk.END, "使用步骤:\n"
                     "1. 选择工作目录\n"
                     "2. 查看待分类文件列表\n"
                     "3. 配置分类规则（可选）\n"
                     "4. 点击'开始分类'按钮\n"
                     "5. 分类完成后可点击'生成汇总'创建Word报告\n\n"
                     "快捷键:\n"
                     "- Ctrl+S: 开始分类\n"
                     "- Ctrl+G: 生成汇总\n"
                     "- F5: 刷新文件列表\n"
                     "- Esc: 退出程序")
        text.config(state=tk.DISABLED)

        # 页面3: 完成
        page3 = ttk.Frame(notebook)
        notebook.add(page3, text="完成")

        ttk.Label(page3, text="设置完成！\n\n点击'完成'开始使用工具。",
                 font=default_font).pack(pady=50)

        # 完成按钮
        ttk.Button(wizard, text="完成", command=wizard.destroy).pack(pady=10)

    def browse_directory(self):
        """浏览选择工作目录"""
        directory = filedialog.askdirectory(title="选择工作目录", initialdir=self.work_dir)
        if directory:
            self.work_dir = directory
            self.dir_var.set(directory)
            self.refresh_file_list()
            logging.info(f"工作目录已更改为: {directory}")

    def refresh_category_list(self):
        """刷新分类规则列表"""
        # 清空现有项
        for item in self.category_tree.get_children():
            self.category_tree.delete(item)

        # 添加分类规则
        for config in sorted(self.categories_config, key=lambda x: x.get('priority', 0), reverse=True):
            self.category_tree.insert('', tk.END, values=(config['name'], config['priority']))

    def refresh_file_list(self):
        """刷新文件列表"""
        # 清空现有项
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        self.file_list = []
        # 重置项目ID列表
        self.tree_items = []
        self.status_var.set(f"正在扫描目录: {self.work_dir}")
        self.root.update_idletasks()

        try:
            # 获取选中的文件格式
            allowed_extensions = []
            if self.txt_var.get():
                allowed_extensions.append('.txt')
            if self.doc_var.get():
                allowed_extensions.extend(['.doc', '.docx'])
            if self.pdf_var.get():
                allowed_extensions.append('.pdf')
                
            # 遍历工作目录
            for item in os.listdir(self.work_dir):
                item_path = os.path.join(self.work_dir, item)
                # 跳过文件夹、临时文件、脚本本身和汇总文档
                if os.path.isfile(item_path) and not item.startswith('~$') and item != 'file_sorter_gui.py' and item != '文章汇总.docx' and item != 'file_sorter_log.txt' and os.path.splitext(item)[1].lower() in allowed_extensions:
                    # 获取文件大小
                    size = os.path.getsize(item_path)
                    size_str = self.format_size(size)
                      # 预测分类
                    category = self.get_category(item)
                    
                    # 添加到文件列表
                    self.file_list.append((item, size_str, category, "未处理", item_path))
                    # 保存项目ID
                    item_id = self.file_tree.insert('', tk.END, values=(item, size_str, category, "未处理"))
                    self.tree_items.append(item_id)

            self.total_files = len(self.file_list)
            self.status_var.set(f"就绪 - 共发现 {self.total_files} 个可分类文件")
            logging.info(f"已刷新文件列表，发现 {self.total_files} 个可分类文件")
        except Exception as e:
            self.status_var.set(f"扫描目录时出错")
            logging.error(f"扫描目录时出错: {e}")
            messagebox.showerror("错误", f"扫描目录时出错: {str(e)}")

    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

    def get_category(self, filename):
        """根据文件名获取分类"""
        matched_categories = []
        default_category = None
        
        for config in self.categories_config:
            # 检查是否匹配当前分类规则
            if config['match_type'] == 'contains' and any(keyword in filename for keyword in config['keywords'] if keyword):
                matched_categories.append(config)
            elif config['match_type'] == 'startswith' and any(filename.startswith(keyword) for keyword in config['keywords'] if keyword):
                matched_categories.append(config)
            elif config['match_type'] == 'default':
                default_category = config['name']
        
        if matched_categories:
            # 按优先级排序，取最高优先级的分类
            matched_categories.sort(key=lambda x: x.get('priority', 0), reverse=True)
            return matched_categories[0]['name']
        
        return default_category or "未分类"

    def view_category_keywords(self, event):
        """查看分类关键词"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            return

        category_name = self.category_tree.item(selected_item[0], 'values')[0]
        category = next((c for c in self.categories_config if c['name'] == category_name), None)

        if category:
            keywords = ', '.join(category['keywords']) if category['keywords'] else '无'
            # 修复多行条件表达式语法错误
            match_type = (
                "包含关键词" if category['match_type'] == 'contains' else
                "以关键词开头" if category['match_type'] == 'startswith' else "默认分类"
            )

            msg = f"分类名称: {category['name']}\n"
            msg += f"匹配方式: {match_type}\n"
            msg += f"优先级: {category['priority']}\n"
            msg += f"关键词: {keywords}"

            messagebox.showinfo("分类详情", msg)

    def add_category(self):
        """添加新分类"""
        # 创建对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("添加新分类")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 分类名称
        ttk.Label(dialog, text="分类名称:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)

        # 匹配方式
        ttk.Label(dialog, text="匹配方式:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        match_type_var = tk.StringVar(value='contains')
        match_frame = ttk.Frame(dialog)
        match_frame.grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)
        ttk.Radiobutton(match_frame, text="包含关键词", variable=match_type_var, value='contains').pack(side=tk.LEFT)
        ttk.Radiobutton(match_frame, text="以关键词开头", variable=match_type_var, value='startswith').pack(side=tk.LEFT)
        ttk.Radiobutton(match_frame, text="默认分类", variable=match_type_var, value='default').pack(side=tk.LEFT)

        # 优先级
        ttk.Label(dialog, text="优先级:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        priority_var = tk.IntVar(value=1)
        ttk.Spinbox(dialog, from_=1, to=10, textvariable=priority_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        # 关键词
        ttk.Label(dialog, text="关键词 (每行一个):").grid(row=3, column=0, sticky=tk.NW, padx=10, pady=5)
        keywords_text = scrolledtext.ScrolledText(dialog, width=30, height=8)
        keywords_text.grid(row=3, column=1, padx=10, pady=5)

        # 按钮
        btn_frame = ttk.Frame(dialog)
        btn_frame.grid(row=4, column=0, columnspan=2, pady=10)

        def save_category():
            name = name_var.get().strip()
            if not name:
                messagebox.showwarning("警告", "分类名称不能为空")
                return

            # 检查名称是否已存在
            if any(c['name'] == name for c in self.categories_config):
                messagebox.showwarning("警告", "此分类名称已存在")
                return

            # 检查是否已存在默认分类
            if match_type_var.get() == 'default' and any(c['match_type'] == 'default' for c in self.categories_config):
                messagebox.showwarning("警告", "只能有一个默认分类")
                return

            # 获取关键词
            keywords = [line.strip() for line in keywords_text.get('1.0', tk.END).split('\n') if line.strip()]

            # 添加新分类
            self.categories_config.append({
                'name': name,
                'keywords': keywords,
                'match_type': match_type_var.get(),
                'priority': priority_var.get()
            })

            # 保存配置并刷新列表
            self.save_config()
            self.refresh_category_list()
            dialog.destroy()
            logging.info(f"已添加新分类: {name}")

        ttk.Button(btn_frame, text="保存", command=save_category).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def edit_category(self):
        """编辑选中的分类"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请先选择一个分类")
            return

        category_name = self.category_tree.item(selected_item[0], 'values')[0]
        category = next((c for c in self.categories_config if c['name'] == category_name), None)

        if not category:
            return

        # 创建对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑分类")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 分类名称
        ttk.Label(dialog, text="分类名称:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        name_var = tk.StringVar(value=category['name'])
        name_entry = ttk.Entry(dialog, textvariable=name_var, width=30)
        name_entry.grid(row=0, column=1, padx=10, pady=5)

        # 匹配方式
        ttk.Label(dialog, text="匹配方式:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        match_type_var = tk.StringVar(value=category['match_type'])
        match_frame = ttk.Frame(dialog)
        match_frame.grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)
        ttk.Radiobutton(match_frame, text="包含关键词", variable=match_type_var, value='contains').pack(side=tk.LEFT)
        ttk.Radiobutton(match_frame, text="以关键词开头", variable=match_type_var, value='startswith').pack(side=tk.LEFT)
        ttk.Radiobutton(match_frame, text="默认分类", variable=match_type_var, value='default').pack(side=tk.LEFT)

        # 优先级
        ttk.Label(dialog, text="优先级:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        priority_var = tk.IntVar(value=category['priority'])
        ttk.Spinbox(dialog, from_=1, to=10, textvariable=priority_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        # 关键词
        ttk.Label(dialog, text="关键词 (每行一个):").grid(row=3, column=0, sticky=tk.NW, padx=10, pady=5)
        keywords_text = scrolledtext.ScrolledText(dialog, width=30, height=8)
        keywords_text.grid(row=3, column=1, padx=10, pady=5)
        keywords_text.insert(tk.END, '\n'.join(category['keywords']))

        # 按钮
        btn_frame = ttk.Frame(dialog)
        btn_frame.grid(row=4, column=0, columnspan=2, pady=10)

        def save_edited_category():
            name = name_var.get().strip()
            if not name:
                messagebox.showwarning("警告", "分类名称不能为空")
                return

            # 检查名称是否已存在（排除当前分类）
            if any(c['name'] == name and c['name'] != category_name for c in self.categories_config):
                messagebox.showwarning("警告", "此分类名称已存在")
                return

            # 检查是否已存在默认分类（排除当前分类）
            if match_type_var.get() == 'default' and any(c['match_type'] == 'default' and c['name'] != category_name for c in self.categories_config):
                messagebox.showwarning("警告", "只能有一个默认分类")
                return

            # 获取关键词
            keywords = [line.strip() for line in keywords_text.get('1.0', tk.END).split('\n') if line.strip()]

            # 更新分类
            index = self.categories_config.index(category)
            self.categories_config[index] = {
                'name': name,
                'keywords': keywords,
                'match_type': match_type_var.get(),
                'priority': priority_var.get()
            }

            # 保存配置并刷新列表
            self.save_config()
            self.refresh_category_list()
            self.refresh_file_list()  # 刷新文件分类预测
            dialog.destroy()
            logging.info(f"已更新分类: {name}")

        ttk.Button(btn_frame, text="保存", command=save_edited_category).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def delete_category(self):
        """删除选中的分类"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请先选择一个分类")
            return

        category_name = self.category_tree.item(selected_item[0], 'values')[0]
        category = next((c for c in self.categories_config if c['name'] == category_name), None)

        if not category:
            return

        # 不能删除默认分类
        if category['match_type'] == 'default':
            messagebox.showwarning("警告", "不能删除默认分类")
            return

        if messagebox.askyesno("确认", f"确定要删除分类 '{category_name}' 吗?"):
            self.categories_config.remove(category)
            self.save_config()
            self.refresh_category_list()
            self.refresh_file_list()  # 刷新文件分类预测
            logging.info(f"已删除分类: {category_name}")

    def increase_priority(self):
        """提高选中分类的优先级"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请先选择一个分类")
            return

        category_name = self.category_tree.item(selected_item[0], 'values')[0]
        category = next((c for c in self.categories_config if c['name'] == category_name), None)

        if category:
            category['priority'] += 1
            self.save_config()
            self.refresh_category_list()
            self.refresh_file_list()  # 刷新文件分类预测
            logging.info(f"已提高分类 '{category_name}' 的优先级至 {category['priority']}")

    def decrease_priority(self):
        """降低选中分类的优先级"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请先选择一个分类")
            return

        category_name = self.category_tree.item(selected_item[0], 'values')[0]
        category = next((c for c in self.categories_config if c['name'] == category_name), None)

        if category and category['priority'] > 1:
            category['priority'] -= 1
            self.save_config()
            self.refresh_category_list()
            self.refresh_file_list()  # 刷新文件分类预测
            logging.info(f"已降低分类 '{category_name}' 的优先级至 {category['priority']}")

    def show_file_menu(self, event):
        """显示文件右键菜单"""
        item = self.file_tree.identify_row(event.y)
        if item:
            self.file_tree.selection_set(item)
            self.file_menu.post(event.x_root, event.y_root)

    def open_selected_file(self):
        """打开选中的文件"""
        selected_item = self.file_tree.selection()
        if not selected_item:
            return

        index = int(selected_item[0])
        if 0 <= index < len(self.file_list):
            file_path = self.file_list[index][4]
            try:
                os.startfile(file_path)
                logging.info(f"已打开文件: {file_path}")
            except Exception as e:
                logging.error(f"打开文件失败: {e}")
                messagebox.showerror("错误", f"打开文件失败: {str(e)}")

    def view_file_properties(self):
        """查看选中文件的属性"""
        selected_item = self.file_tree.selection()
        if not selected_item:
            return

        index = int(selected_item[0])
        if 0 <= index < len(self.file_list):
            file_name, size, category, status, file_path = self.file_list[index]
            created_time = datetime.fromtimestamp(os.path.getctime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
            modified_time = datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')

            msg = f"文件名: {file_name}\n"
            msg += f"路径: {file_path}\n"
            msg += f"大小: {size}\n"
            msg += f"创建时间: {created_time}\n"
            msg += f"修改时间: {modified_time}\n"
            msg += f"预测分类: {category}\n"
            msg += f"状态: {status}"

            messagebox.showinfo("文件属性", msg)

    def manual_classify(self):
        """手动分类选中的文件"""
        selected_item = self.file_tree.selection()
        if not selected_item:
            return

        index = int(selected_item[0])
        if 0 <= index < len(self.file_list):
            file_name, size, category, status, file_path = self.file_list[index]

            # 创建分类选择对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("手动分类")
            dialog.geometry("300x200")
            dialog.transient(self.root)
            dialog.grab_set()

            ttk.Label(dialog, text=f"为文件 '{file_name}' 选择分类:").pack(pady=10)

            category_var = tk.StringVar(value=category)
            category_combo = ttk.Combobox(dialog, textvariable=category_var, state='readonly', width=20)
            category_combo['values'] = [c['name'] for c in self.categories_config]
            category_combo.pack(pady=10)

            btn_frame = ttk.Frame(dialog)
            btn_frame.pack(pady=20)

            def do_classify():
                selected_category = category_var.get()
                if not selected_category:
                    messagebox.showwarning("警告", "请选择分类")
                    return

                # 更新文件列表中的分类
                self.file_list[index] = (file_name, size, selected_category, status, file_path)
                self.file_tree.item(selected_item[0], values=(file_name, size, selected_category, status))
                dialog.destroy()

            ttk.Button(btn_frame, text="确定", command=do_classify).pack(side=tk.LEFT, padx=10)
            ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def start_processing(self):
        """开始文件分类处理"""
        if self.processing:
            messagebox.showinfo("提示", "正在处理中，请稍候...")
            return

        if not self.file_list:
            messagebox.showinfo("提示", "没有可分类的文件")
            return

        if messagebox.askyesno("确认", f"确定要对 {self.total_files} 个文件进行分类吗?"):
            # 创建分类文件夹（如果不存在）
            for config in self.categories_config:
                category_path = os.path.join(self.work_dir, config['name'])
                if not os.path.exists(category_path):
                    os.makedirs(category_path)
                    logging.info(f"创建分类文件夹: {category_path}")

            # 重置状态
            self.processed_files = 0
            self.progress_var.set(0)
            self.processing = True
            self.status_var.set("正在处理...")

            # 在新线程中处理文件
            threading.Thread(target=self.process_files, daemon=True).start()

    def process_files(self):
        """处理文件分类"""
        try:
            # 初始化分类文件映射
            self.category_files = {}
            self.processed_titles = set()

            for i, file_info in enumerate(self.file_list):
                if not self.processing:  # 检查是否需要取消
                    break

                file_name, size, category, status, file_path = file_info
                self.status_var.set(f"正在处理: {file_name}")

                try:
                    # 提取文件内容（用于汇总）
                    title, content = self.extract_file_content(file_path)

                    # 基于标题去重检查
                    # 添加到分类集合
                    if category not in self.category_files:
                        self.category_files[category] = []
                    self.category_files[category].append((title, content))
                    self.processed_titles.add(title)

                    # 文件分类复制（替代移动）
                    dest_dir = os.path.join(self.work_dir, category)
                    dest_path = os.path.join(dest_dir, file_name)

                    # 如果目标文件已存在，添加数字后缀避免覆盖
                    if os.path.exists(dest_path):
                        counter = 1
                        while True:
                            name, ext = os.path.splitext(file_name)
                            new_name = f"{name}_{counter}{ext}"
                            dest_path = os.path.join(dest_dir, new_name)
                            if not os.path.exists(dest_path):
                                break
                            counter += 1

                    shutil.copy2(file_path, dest_path)  # 使用copy2保留元数据
                    self.file_tree.item(self.tree_items[i], values=(file_name, size, category, "已完成"))
                    self.file_list[i] = (file_name, size, category, "已完成", dest_path)
                    logging.info(f"已复制: {file_name} -> {category}/{os.path.basename(dest_path)}")

                except Exception as e:
                    self.file_tree.item(self.tree_items[i], values=(file_name, size, category, "出错"))
                    self.file_list[i] = (file_name, size, category, "出错", file_path)
                    logging.error(f"处理文件 {file_name} 时出错: {e}")

                # 更新进度
                self.processed_files += 1
                progress = (self.processed_files / self.total_files) * 100
                self.progress_var.set(progress)
                self.root.update_idletasks()

            # 处理完成
            self.processing = False
            self.status_var.set(f"处理完成 - 成功: {self.processed_files - self.file_list.count('出错')} 个, 失败: {self.file_list.count('出错')} 个")
            logging.info(f"分类处理完成 - 共处理 {self.processed_files} 个文件")

            # 播放提示音
            winsound.Beep(1000, 500)  # 频率1000Hz，持续500ms

            # 询问是否生成汇总
            if messagebox.askyesno("处理完成", "文件分类已完成，是否立即生成汇总文档?"):
                self.generate_summary()

        except Exception as e:
            self.processing = False
            self.status_var.set("处理出错")
            logging.error(f"分类处理出错: {e}")
            messagebox.showerror("错误", f"分类处理出错: {str(e)}")

    def extract_file_content(self, file_path):
        """提取不同类型文件内容"""
        file_ext = os.path.splitext(file_path)[1].lower()
        content = ''
        try:
            if file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
            elif file_ext == '.docx':
                doc = docx.Document(file_path)
                content = '\n'.join([para.text for para in doc.paragraphs])
            elif file_ext == '.doc':
                # 需要安装antiword
                content = textract.process(str(file_path)).decode('utf-8', errors='ignore')
            elif file_ext == '.pdf':
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    content = '\n'.join([page.extract_text() for page in reader.pages])
        except Exception as e:
            logging.error(f"提取文件 {os.path.basename(file_path)} 内容失败: {e}")
            content = f"[提取内容失败: {str(e)}]"

        # 添加文本标准化处理
        if content:
            # 去除多余空白和标准化换行符
            content = ' '.join(content.split())

        # 使用文件名作为标题
        title = os.path.splitext(os.path.basename(file_path))[0]
        return title, content

    def generate_summary(self):
        """生成汇总文档"""
        if not self.category_files or not any(self.category_files.values()):
            messagebox.showinfo("提示", "没有可汇总的文件内容，请先进行分类处理")
            return

        self.status_var.set("正在生成汇总文档...")
        self.root.update_idletasks()

        try:
            # 初始化汇总文档
            summary_doc = self.init_summary_doc()

            # 添加目录
            toc_para = self.add_table_of_contents(summary_doc)
            summary_doc.add_page_break()

            # 按分类优先级排序并添加内容
            category_priority = {config['name']: config.get('priority', 0) for config in self.categories_config}
            for category in sorted(self.category_files.keys(), key=lambda x: category_priority.get(x, 0), reverse=True):
                self.add_title_to_summary(summary_doc, category, level=1)
                for title, content in self.category_files[category]:
                    self.add_title_to_summary(summary_doc, title, level=2)
                    self.add_content_to_summary(summary_doc, content)

            # 将目录移动到文档开头
            summary_doc._body._element.insert(0, toc_para._element)

            # 移除原位置的空段落
            for para in summary_doc.paragraphs:
                if not para.text.strip():
                    p = para._element
                    p.getparent().remove(p)
                    break

            # 保存汇总文档 - 添加时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_filename = f"文章汇总_{timestamp}.docx"
            summary_path = os.path.join(self.work_dir, summary_filename)
            summary_doc.save(summary_path)

            self.status_var.set(f"汇总文档已生成: {summary_path}")
            logging.info(f"汇总文档已生成: {summary_path}")
            messagebox.showinfo("成功", f"汇总文档已生成:\n{summary_path}\n\n提示: 打开文档后需右键点击目录选择'更新域'以刷新目录内容和页码")

            # 询问是否打开文档
            if messagebox.askyesno("打开文档", "是否立即打开汇总文档?"):
                os.startfile(summary_path)

        except Exception as e:
            self.status_var.set("生成汇总文档失败")
            logging.error(f"生成汇总文档失败: {e}")
            messagebox.showerror("错误", f"生成汇总文档失败: {str(e)}")

    def init_summary_doc(self):
        """初始化汇总文档"""
        doc = docx.Document()
        # 设置页边距
        sections = doc.sections
        for section in sections:
            section.top_margin = Cm(3.7)
            section.bottom_margin = Cm(3.5)
            section.left_margin = Cm(2.8)
            section.right_margin = Cm(2.6)
            
            # 添加居中页码
            footer = section.footer
            para = footer.paragraphs[0]
            para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = para.add_run()
            fld_char = OxmlElement('w:fldChar')
            fld_char.set(qn('w:fldCharType'), 'begin')
            run._r.append(fld_char)
            
            instr_text = OxmlElement('w:instrText')
            instr_text.text = "PAGE"
            run._r.append(instr_text)
            
            fld_char = OxmlElement('w:fldChar')
            fld_char.set(qn('w:fldCharType'), 'end')
            run._r.append(fld_char)
        return doc

    def add_title_to_summary(self, doc, title, level):
        """添加指定级别的标题到汇总文档"""
        title_para = doc.add_paragraph(title, style=f'Heading {level}')
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        title_run = title_para.runs[0]
        title_run.font.name = '方正小标宋简体'
        title_run._element.rPr.rFonts.set(qn('w:eastAsia'), '方正小标宋简体')
        title_run.font.color.rgb = RGBColor(0, 0, 0)  # 设置字体为黑色
        
        if level == 1:
            title_run.font.size = Pt(22)  # 一级标题字号
        else:
            title_run.font.size = Pt(16)  # 二级标题字号
            
        title_run.font.bold = True
        doc.add_paragraph()  # 添加空行

    def add_content_to_summary(self, doc, content):
        """添加正文内容到汇总文档"""
        content_para = doc.add_paragraph()
        content_run = content_para.add_run(content)
        content_run.font.name = '仿宋GB2312'
        content_run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋GB2312')
        content_run.font.size = Pt(16)  # 三号字对应16pt
        content_run.font.color.rgb = RGBColor(0, 0, 0)  # 设置字体为黑色
        # 设置首行缩进2个字符
        content_para.paragraph_format.first_line_indent = Pt(32)
        # 设置行距28.95磅
        content_para.paragraph_format.line_spacing = Pt(28.95)
        doc.add_page_break()  # 分页

    def add_table_of_contents(self, doc):
        """添加目录并返回目录段落"""
        para = doc.add_paragraph()
        run = para.add_run()
        
        # 添加目录字段代码
        fld_char_begin = OxmlElement('w:fldChar')
        fld_char_begin.set(qn('w:fldCharType'), 'begin')
        run._r.append(fld_char_begin)
        
        instr_text = OxmlElement('w:instrText')
        instr_text.text = r'TOC \o "1-2" \h \z \u'  # 显示1-2级标题，带超链接
        run._r.append(instr_text)
        
        fld_char_end = OxmlElement('w:fldChar')
        fld_char_end.set(qn('w:fldCharType'), 'end')
        run._r.append(fld_char_end)
        
        return para  # 返回目录段落

if __name__ == "__main__":
    # 确保中文显示正常 - 使用Tkinter内置字体配置替代matplotlib
    import tkinter.font as font
    
    root = tk.Tk()
    # 设置默认字体为支持中文的字体
    default_font = font.nametofont("TkDefaultFont")
    default_font.configure(family=["SimHei", "WenQuanYi Micro Hei", "Heiti TC"], size=10)
    root.option_add("*Font", default_font)
    
    app = FileSorterApp(root)
    root.mainloop()
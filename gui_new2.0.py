"""
文件自动分类工具 GUI 2.0版本
专业现代、层级清晰、交互友好的界面设计
基于模块化重构，实现企业级UI体验
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
import time
import json
from datetime import datetime
import docx
from docx.shared import Cm, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
import PyPDF2
import textract

# 导入核心模块
from core.file_processor import FileProcessor
from core.config_manager import ConfigManager as CoreConfigManager
from core.document_generator import DocumentGenerator
from core.exceptions import FileSorterError
from core.logger import setup_module_logger


class ConfigManager:
    """配置管理适配器 - 为GUI提供统一的配置接口"""

    def __init__(self):
        self.core_config = CoreConfigManager()
        self.gui_config_file = 'gui_config.json'
        self.gui_config = self._load_gui_config()

    def _load_gui_config(self):
        """加载GUI配置"""
        default_config = {
            'target_path': './已分类',
            'auto_refresh': True,
            'show_notifications': True,
            'font_size': '中等',
            'color_scheme': '默认蓝色',
            'batch_size': 100,
            'enable_logging': True,
            'classification_rules': []
        }

        if os.path.exists(self.gui_config_file):
            try:
                with open(self.gui_config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception:
                pass

        return default_config

    def get_config(self):
        """获取完整配置"""
        return self.gui_config.copy()

    def update_config(self, new_config):
        """更新配置"""
        self.gui_config.update(new_config)
        self._save_gui_config()

    def _save_gui_config(self):
        """保存GUI配置"""
        try:
            with open(self.gui_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.gui_config, f, ensure_ascii=False, indent=4)
        except Exception:
            pass

    # 代理核心配置管理器的方法
    def get_categories(self):
        return self.core_config.get_categories()

    def add_category(self, name, keywords, match_type='contains', priority=1):
        return self.core_config.add_category(name, keywords, match_type, priority)

    def update_category(self, old_name, new_name, keywords, match_type='contains', priority=1):
        return self.core_config.update_category(old_name, new_name, keywords, match_type, priority)

    def delete_category(self, name):
        return self.core_config.delete_category(name)

    def get_category_by_name(self, name):
        return self.core_config.get_category_by_name(name)

    def get_category_for_filename(self, filename):
        return self.core_config.get_category_for_filename(filename)


class ModernTheme:
    """现代化主题配置 - GUI 2.0专用 - 增强版"""

    # 当前主题
    current_theme = "默认蓝色"
    current_font_size = "中等"

    # 主题配色方案
    THEME_COLORS = {
        "默认蓝色": {
            'primary_start': '#4CAFFE',
            'primary_end': '#2E8EE0',
            'primary_hover': '#5BB6FF',
            'accent': '#FF6B6B',
            'accent_hover': '#FF5252',
        },
        "商务灰色": {
            'primary_start': '#607D8B',
            'primary_end': '#455A64',
            'primary_hover': '#546E7A',
            'accent': '#FF9800',
            'accent_hover': '#F57C00',
        },
        "护眼绿色": {
            'primary_start': '#4CAF50',
            'primary_end': '#388E3C',
            'primary_hover': '#43A047',
            'accent': '#FF5722',
            'accent_hover': '#E64A19',
        },
        "温暖橙色": {
            'primary_start': '#FF9800',
            'primary_end': '#F57C00',
            'primary_hover': '#FB8C00',
            'accent': '#2196F3',
            'accent_hover': '#1976D2',
        }
    }

    # 字体大小配置
    FONT_SIZES = {
        "小": {
            'title': ('Microsoft YaHei', 14, 'bold'),
            'subtitle': ('Microsoft YaHei', 12, 'bold'),
            'content': ('Microsoft YaHei', 9),
            'auxiliary': ('Microsoft YaHei', 8),
            'caption': ('Microsoft YaHei', 7),
            'button': ('Microsoft YaHei', 9),
            'button_large': ('Microsoft YaHei', 10, 'bold'),
            'button_small': ('Microsoft YaHei', 8),
            'small': ('Microsoft YaHei', 8),
            'monospace': ('Consolas', 8),
        },
        "中等": {
            'title': ('Microsoft YaHei', 16, 'bold'),
            'subtitle': ('Microsoft YaHei', 14, 'bold'),
            'content': ('Microsoft YaHei', 10),
            'auxiliary': ('Microsoft YaHei', 9),
            'caption': ('Microsoft YaHei', 8),
            'button': ('Microsoft YaHei', 10),
            'button_large': ('Microsoft YaHei', 12, 'bold'),
            'button_small': ('Microsoft YaHei', 9),
            'small': ('Microsoft YaHei', 9),
            'monospace': ('Consolas', 10),
        },
        "大": {
            'title': ('Microsoft YaHei', 18, 'bold'),
            'subtitle': ('Microsoft YaHei', 16, 'bold'),
            'content': ('Microsoft YaHei', 12),
            'auxiliary': ('Microsoft YaHei', 11),
            'caption': ('Microsoft YaHei', 10),
            'button': ('Microsoft YaHei', 12),
            'button_large': ('Microsoft YaHei', 14, 'bold'),
            'button_small': ('Microsoft YaHei', 11),
            'small': ('Microsoft YaHei', 10),
            'monospace': ('Consolas', 12),
        },
        "特大": {
            'title': ('Microsoft YaHei', 20, 'bold'),
            'subtitle': ('Microsoft YaHei', 18, 'bold'),
            'content': ('Microsoft YaHei', 14),
            'auxiliary': ('Microsoft YaHei', 13),
            'caption': ('Microsoft YaHei', 12),
            'button': ('Microsoft YaHei', 14),
            'button_large': ('Microsoft YaHei', 16, 'bold'),
            'button_small': ('Microsoft YaHei', 13),
            'small': ('Microsoft YaHei', 12),
            'monospace': ('Consolas', 14),
        }
    }

    # 色彩系统（全局变量化）
    COLORS = {
        # 主背景色
        'nav_bg': '#F2F7FA',        # 顶部导航/操作区（浅蓝灰）
        'tree_bg': '#F9FBFC',       # 左侧目录树（浅灰蓝）
        'list_bg': '#FFFFFF',       # 文件列表区（纯白）
        'config_bg': '#FAFAFA',     # 分类配置区（浅灰）

        # 功能色 - 增强渐变系统
        'primary_start': '#4CAFFE',  # 主按钮渐变起始
        'primary_end': '#2E8EE0',    # 主按钮渐变结束
        'primary_hover': '#5BB6FF',  # 主按钮悬停（亮度+10%）
        'primary_active': '#1E7DD0', # 主按钮按下（亮度-10%）
        'primary_disabled': '#B8D4F0', # 主按钮禁用
        'accent_start': '#FF9F40',   # 备选橙色渐变起始
        'accent_end': '#FF8C29',     # 备选橙色渐变结束
        'accent_hover': '#FFB055',   # 橙色悬停

        # 文字色
        'text_primary': '#333333',   # 主文字（深灰）
        'text_secondary': '#666666', # 辅助文字（中灰）
        'text_muted': '#999999',     # 提示文字（浅灰）
        'text_white': '#FFFFFF',     # 白色文字
        'text_link': '#4CAFFE',      # 链接文字

        # 交互色 - 增强反馈系统
        'hover_bg': '#F5F8FE',       # 悬停背景（浅蓝灰）
        'hover_bg_strong': '#E8F2FF', # 强悬停背景
        'selected_bg': '#E3F2FD',    # 选中背景
        'selected_border': '#4CAFFE', # 选中边框（蓝色）
        'focus_border': '#2E8EE0',   # 焦点边框
        'card_shadow': 'rgba(0,0,0,0.05)', # 卡片阴影
        'card_shadow_hover': 'rgba(0,0,0,0.12)', # 卡片悬停阴影（增强）
        'card_shadow_active': 'rgba(0,0,0,0.18)', # 卡片激活阴影

        # 状态色 - 完整状态系统
        'success_bg': '#E8F5E8',     # 成功背景（调整）
        'success_text': '#2E7D32',   # 成功文字（深绿）
        'success_border': '#4CAF50', # 成功边框
        'error_bg': '#FFEBEE',       # 错误背景（调整）
        'error_text': '#D32F2F',     # 错误文字（深红）
        'error_border': '#F44336',   # 错误边框
        'warning_bg': '#FFF8E1',     # 警告背景
        'warning_text': '#F57C00',   # 警告文字
        'warning_border': '#FF9800', # 警告边框
        'info_bg': '#E3F2FD',        # 信息背景
        'info_text': '#1976D2',      # 信息文字
        'info_border': '#2196F3',    # 信息边框

        # 滚动条
        'scroll_track': '#F5F5F5',   # 滚动条轨道（调整）
        'scroll_thumb': '#C0C0C0',   # 滚动条滑块（调整）
        'scroll_thumb_hover': '#A0A0A0', # 滚动条滑块悬停（调整）

        # 分隔线和边框
        'border_light': '#E0E0E0',   # 浅色边框
        'border_medium': '#BDBDBD',  # 中等边框
        'border_dark': '#757575',    # 深色边框
        'divider': '#F0F0F0',        # 分隔线
    }
    
    # 字体规范 - 增强版
    FONTS = {
        'title': ('Microsoft YaHei', 16, 'bold'),        # 模块标题（加粗）
        'subtitle': ('Microsoft YaHei', 14, 'bold'),     # 子标题
        'content': ('Microsoft YaHei', 14, 'normal'),    # 内容文字
        'auxiliary': ('Microsoft YaHei', 12, 'normal'),  # 辅助文字
        'caption': ('Microsoft YaHei', 11, 'normal'),    # 说明文字
        'button': ('Microsoft YaHei', 14, 'normal'),     # 按钮文字
        'button_large': ('Microsoft YaHei', 16, 'bold'), # 大按钮文字（加粗）
        'button_small': ('Microsoft YaHei', 12, 'normal'), # 小按钮文字
        'monospace': ('Consolas', 12, 'normal'),         # 等宽字体
    }

    # 尺寸规范 - 增强版
    SIZES = {
        # 圆角系统
        'card_radius': 8,           # 卡片圆角（增大）
        'button_radius': 6,         # 按钮圆角（增大）
        'input_radius': 4,          # 输入框圆角
        'toast_radius': 8,          # Toast圆角

        # 图标系统
        'icon_tiny': 12,            # 微小图标
        'icon_small': 16,           # 小图标
        'icon_medium': 20,          # 中图标（调整）
        'icon_large': 24,           # 大图标
        'icon_xlarge': 32,          # 超大图标

        # 间距系统
        'spacing_xs': 4,            # 极小间距
        'spacing_sm': 8,            # 小间距
        'spacing_md': 12,           # 中间距
        'spacing_lg': 16,           # 大间距
        'spacing_xl': 24,           # 超大间距
        'spacing_xxl': 32,          # 超超大间距

        # 组件尺寸
        'button_height': 36,        # 标准按钮高度
        'button_height_small': 28,  # 小按钮高度
        'button_height_large': 44,  # 大按钮高度
        'button_large_width': 140,  # 大按钮宽度（增大）
        'input_height': 32,         # 输入框高度
        'search_width': 260,        # 搜索框宽度（增大）
        'scroll_width': 8,          # 滚动条宽度（增大）

        # 布局尺寸
        'sidebar_width': 280,       # 侧边栏宽度
        'panel_min_width': 200,     # 面板最小宽度
        'header_height': 70,        # 头部高度（增大）
        'footer_height': 32,        # 底部高度
        'indent': 20,               # 目录树缩进（增大）

        # 阴影和边框
        'border_width': 1,          # 边框宽度
        'shadow_blur': 8,           # 阴影模糊度
        'shadow_offset': 2,         # 阴影偏移
    }

    @classmethod
    def apply_theme(cls, theme_name):
        """应用主题"""
        if theme_name in cls.THEME_COLORS:
            cls.current_theme = theme_name
            theme_colors = cls.THEME_COLORS[theme_name]

            # 更新主要颜色
            cls.COLORS.update({
                'primary_start': theme_colors['primary_start'],
                'primary_end': theme_colors['primary_end'],
                'primary_hover': theme_colors['primary_hover'],
                'accent': theme_colors['accent'],
                'accent_hover': theme_colors['accent_hover'],
            })

    @classmethod
    def apply_font_size(cls, size_name):
        """应用字体大小"""
        if size_name in cls.FONT_SIZES:
            cls.current_font_size = size_name
            cls.FONTS = cls.FONT_SIZES[size_name].copy()

    @classmethod
    def get_current_theme(cls):
        """获取当前主题"""
        return cls.current_theme

    @classmethod
    def get_current_font_size(cls):
        """获取当前字体大小"""
        return cls.current_font_size


class ModernCard(ttk.Frame):
    """现代化卡片组件"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        self.title = title
        self.create_card()
    
    def create_card(self):
        """创建卡片结构"""
        # 配置卡片样式
        self.configure(style='Card.TFrame', padding=ModernTheme.SIZES['spacing_md'])
        
        if self.title:
            # 卡片标题 - 优化布局确保完整显示
            title_frame = ttk.Frame(self)
            title_frame.pack(fill=tk.X, pady=(0, ModernTheme.SIZES['spacing_md']))

            title_label = ttk.Label(
                title_frame,
                text=self.title,
                font=ModernTheme.FONTS['title'],
                foreground=ModernTheme.COLORS['text_primary'],
                anchor='center'  # 居中对齐
            )
            title_label.pack(fill=tk.X, expand=True)  # 居中显示标题
        
        # 卡片内容区域
        self.content_frame = ttk.Frame(self)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
    
    def get_content_frame(self):
        """获取内容框架"""
        return self.content_frame


class ModernButton(ttk.Button):
    """现代化按钮组件 - 增强版"""

    def __init__(self, parent, text="", icon="", style_type="secondary",
                 size="normal", width=None, command=None, **kwargs):
        self.style_type = style_type
        self.icon = icon
        self.original_text = text
        self.size = size
        self.is_loading = False

        # 组合图标和文字
        display_text = f"{icon} {text}" if icon else text

        super().__init__(parent, text=display_text, command=command, **kwargs)

        # 设置按钮样式
        self.configure_style(width)
        self.bind_hover_effects()

    def configure_style(self, width):
        """配置按钮样式 - 增强版"""
        style_map = {
            "primary": f'Primary.{self.size.title()}.Modern.TButton',
            "secondary": f'Secondary.{self.size.title()}.Modern.TButton',
            "accent": f'Accent.{self.size.title()}.Modern.TButton',
            "success": f'Success.{self.size.title()}.Modern.TButton',
            "danger": f'Danger.{self.size.title()}.Modern.TButton',
            "outline": f'Outline.{self.size.title()}.Modern.TButton'
        }

        style_name = style_map.get(self.style_type, f'Secondary.{self.size.title()}.Modern.TButton')
        self.configure(style=style_name)

        if width:
            self.configure(width=width)

    def bind_hover_effects(self):
        """绑定悬停效果 - 增强版"""
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)
        self.bind('<Button-1>', self.on_click)
        self.bind('<ButtonRelease-1>', self.on_release)

    def on_enter(self, event):
        """鼠标进入效果 - 增强版"""
        if not self.is_loading and self['state'] != 'disabled':
            hover_style = f'{self.style_type.title()}.Hover.{self.size.title()}.Modern.TButton'
            self.configure(style=hover_style)

    def on_leave(self, event):
        """鼠标离开效果 - 增强版"""
        if not self.is_loading and self['state'] != 'disabled':
            normal_style = f'{self.style_type.title()}.{self.size.title()}.Modern.TButton'
            self.configure(style=normal_style)

    def on_click(self, event):
        """鼠标按下效果"""
        if not self.is_loading and self['state'] != 'disabled':
            active_style = f'{self.style_type.title()}.Active.{self.size.title()}.Modern.TButton'
            self.configure(style=active_style)

    def on_release(self, event):
        """鼠标释放效果"""
        if not self.is_loading and self['state'] != 'disabled':
            hover_style = f'{self.style_type.title()}.Hover.{self.size.title()}.Modern.TButton'
            self.configure(style=hover_style)

    def set_loading(self, loading=True):
        """设置加载状态 - 增强版"""
        self.is_loading = loading
        if loading:
            self.configure(text="⟳ 处理中...", state='disabled')
            disabled_style = f'{self.style_type.title()}.Disabled.{self.size.title()}.Modern.TButton'
            self.configure(style=disabled_style)
        else:
            display_text = f"{self.icon} {self.original_text}" if self.icon else self.original_text
            self.configure(text=display_text, state='normal')
            normal_style = f'{self.style_type.title()}.{self.size.title()}.Modern.TButton'
            self.configure(style=normal_style)

    def set_style_type(self, new_style_type):
        """动态更改按钮样式类型"""
        self.style_type = new_style_type
        self.configure_style(None)


class ToastNotification:
    """Toast通知组件 - 增强版"""

    def __init__(self, parent):
        self.parent = parent
        self.toast_window = None
        self.animation_id = None

    def show(self, message, toast_type="info", duration=3000):
        """显示Toast通知 - 增强版"""
        if self.toast_window:
            self.toast_window.destroy()

        # 创建Toast窗口
        self.toast_window = tk.Toplevel(self.parent)
        self.toast_window.withdraw()  # 先隐藏
        self.toast_window.overrideredirect(True)  # 无边框
        self.toast_window.attributes('-topmost', True)  # 置顶

        # 根据类型设置样式
        if toast_type == "success":
            bg_color = ModernTheme.COLORS['success_bg']
            text_color = ModernTheme.COLORS['success_text']
            border_color = ModernTheme.COLORS['success_border']
            icon = "✅"
        elif toast_type == "error":
            bg_color = ModernTheme.COLORS['error_bg']
            text_color = ModernTheme.COLORS['error_text']
            border_color = ModernTheme.COLORS['error_border']
            icon = "❌"
        elif toast_type == "warning":
            bg_color = ModernTheme.COLORS['warning_bg']
            text_color = ModernTheme.COLORS['warning_text']
            border_color = ModernTheme.COLORS['warning_border']
            icon = "⚠️"
        else:  # info
            bg_color = ModernTheme.COLORS['info_bg']
            text_color = ModernTheme.COLORS['info_text']
            border_color = ModernTheme.COLORS['info_border']
            icon = "ℹ️"

        # 创建主框架
        main_frame = tk.Frame(
            self.toast_window,
            bg=bg_color,
            relief='solid',
            bd=1,
            highlightbackground=border_color,
            highlightthickness=2
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # 内容框架
        content_frame = tk.Frame(main_frame, bg=bg_color)
        content_frame.pack(fill=tk.BOTH, expand=True,
                          padx=ModernTheme.SIZES['spacing_md'],
                          pady=ModernTheme.SIZES['spacing_sm'])

        # 图标标签
        icon_label = tk.Label(
            content_frame,
            text=icon,
            font=('Microsoft YaHei', 16),
            bg=bg_color,
            fg=text_color
        )
        icon_label.pack(side=tk.LEFT, padx=(0, ModernTheme.SIZES['spacing_sm']))

        # 消息标签
        message_label = tk.Label(
            content_frame,
            text=message,
            font=ModernTheme.FONTS['content'],
            bg=bg_color,
            fg=text_color,
            wraplength=300
        )
        message_label.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 关闭按钮
        close_btn = tk.Label(
            content_frame,
            text="×",
            font=('Microsoft YaHei', 14, 'bold'),
            bg=bg_color,
            fg=text_color,
            cursor='hand2'
        )
        close_btn.pack(side=tk.RIGHT, padx=(ModernTheme.SIZES['spacing_sm'], 0))
        close_btn.bind('<Button-1>', lambda e: self.hide())

        # 计算位置和大小
        self.toast_window.update_idletasks()
        width = self.toast_window.winfo_reqwidth()
        height = self.toast_window.winfo_reqheight()

        # 获取父窗口位置
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()

        # 计算Toast位置（右上角）
        x = parent_x + parent_width - width - 20
        y = parent_y + 20

        self.toast_window.geometry(f"{width}x{height}+{x}+{y}")

        # 显示动画
        self.show_animation()

        # 自动隐藏
        if duration > 0:
            self.parent.after(duration, self.hide)

    def show_animation(self):
        """显示动画效果"""
        self.toast_window.deiconify()
        self.toast_window.attributes('-alpha', 0.0)

        def fade_in(alpha=0.0):
            alpha += 0.1
            if alpha <= 1.0 and self.toast_window and self.toast_window.winfo_exists():
                self.toast_window.attributes('-alpha', alpha)
                self.animation_id = self.parent.after(20, lambda: fade_in(alpha))
            elif self.toast_window and self.toast_window.winfo_exists():
                self.toast_window.attributes('-alpha', 1.0)

        fade_in()

    def hide(self):
        """隐藏Toast通知"""
        if self.animation_id:
            self.parent.after_cancel(self.animation_id)

        if self.toast_window:
            def fade_out(alpha=1.0):
                alpha -= 0.1
                if alpha >= 0.0 and self.toast_window:
                    self.toast_window.attributes('-alpha', alpha)
                    self.parent.after(20, lambda: fade_out(alpha))
                elif self.toast_window:
                    self.toast_window.destroy()
                    self.toast_window = None

            fade_out()


class LoadingIndicator:
    """加载指示器组件"""
    
    def __init__(self, parent):
        self.parent = parent
        self.loading_frame = None
        self.animation_running = False
    
    def show(self, message="正在加载..."):
        """显示加载指示器"""
        if self.loading_frame:
            return
        
        self.loading_frame = tk.Frame(
            self.parent,
            bg=ModernTheme.COLORS['list_bg']
        )
        self.loading_frame.place(relx=0.5, rely=0.5, anchor=tk.CENTER)
        
        # 加载动画（简化的转圈效果）
        self.loading_label = tk.Label(
            self.loading_frame,
            text="⟳",
            font=('Microsoft YaHei', 24),
            fg=ModernTheme.COLORS['primary_start'],
            bg=ModernTheme.COLORS['list_bg']
        )
        self.loading_label.pack()
        
        # 加载文字
        message_label = tk.Label(
            self.loading_frame,
            text=message,
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_secondary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        message_label.pack(pady=(ModernTheme.SIZES['spacing_sm'], 0))
        
        # 开始动画
        self.start_animation()
    
    def start_animation(self):
        """开始旋转动画"""
        self.animation_running = True
        self.animate()
    
    def animate(self):
        """动画效果 - 增强版"""
        if not self.animation_running or not self.loading_frame:
            return

        # 更丰富的旋转效果
        rotation_chars = ["⟳", "⟲", "⟳", "⟲"]
        colors = [
            ModernTheme.COLORS['primary_start'],
            ModernTheme.COLORS['primary_end'],
            ModernTheme.COLORS['primary_hover'],
            ModernTheme.COLORS['primary_start']
        ]

        if not hasattr(self, 'animation_step'):
            self.animation_step = 0

        char_index = self.animation_step % len(rotation_chars)
        color_index = self.animation_step % len(colors)

        self.loading_label.configure(
            text=rotation_chars[char_index],
            fg=colors[color_index]
        )

        self.animation_step += 1

        # 继续动画
        self.parent.after(300, self.animate)
    
    def hide(self):
        """隐藏加载指示器"""
        self.animation_running = False
        if self.loading_frame:
            self.loading_frame.destroy()
            self.loading_frame = None


class EmptyState:
    """空状态组件"""
    
    @staticmethod
    def create(parent, message, icon="📁"):
        """创建空状态显示"""
        empty_frame = tk.Frame(parent, bg=ModernTheme.COLORS['list_bg'])
        empty_frame.place(relx=0.5, rely=0.5, anchor=tk.CENTER)
        
        # 图标
        icon_label = tk.Label(
            empty_frame,
            text=icon,
            font=('Microsoft YaHei', ModernTheme.SIZES['icon_large']),
            fg=ModernTheme.COLORS['scroll_thumb'],
            bg=ModernTheme.COLORS['list_bg']
        )
        icon_label.pack()
        
        # 主消息
        message_label = tk.Label(
            empty_frame,
            text=message,
            font=ModernTheme.FONTS['subtitle'],
            fg=ModernTheme.COLORS['text_secondary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        message_label.pack(pady=(ModernTheme.SIZES['spacing_sm'], 0))

        # 辅助说明
        help_text = "选择一个目录开始文件分类"
        help_label = tk.Label(
            empty_frame,
            text=help_text,
            font=ModernTheme.FONTS['auxiliary'],
            fg=ModernTheme.COLORS['text_muted'],
            bg=ModernTheme.COLORS['list_bg']
        )
        help_label.pack(pady=(ModernTheme.SIZES['spacing_xs'], 0))

        return empty_frame


class FileSorterApp2:
    """文件自动分类工具 GUI 2.0 主应用"""

    def __init__(self, root):
        self.root = root

        # 首先初始化日志
        self.logger = setup_module_logger('gui_2.0')

        # 初始化配置管理器
        self.config_manager = ConfigManager()

        # 加载保存的主题设置
        self.load_theme_settings()

        self.setup_window()
        self.setup_theme()
        self.init_components()
        self.create_layout()
        self.bind_events()

        # 初始化数据
        self.current_directory = ""
        self.file_list = []
        self.selected_files = []

        # 文档总结相关
        self.category_files = {}  # 存储分类后的文件内容
        self.processed_titles = set()  # 已处理的文件标题（用于去重）

        # 状态管理
        self.is_processing = False

        self.logger.info("GUI 2.0 应用启动")

    def load_theme_settings(self):
        """加载保存的主题设置"""
        try:
            config = self.config_manager.get_config()

            # 加载字体大小设置
            font_size = config.get('font_size', '中等')
            if font_size in ModernTheme.FONT_SIZES:
                ModernTheme.apply_font_size(font_size)

            # 加载颜色方案设置
            color_scheme = config.get('color_scheme', '默认蓝色')
            if color_scheme in ModernTheme.THEME_COLORS:
                ModernTheme.apply_theme(color_scheme)

            self.logger.info(f"已加载主题设置: 字体={font_size}, 颜色方案={color_scheme}")

        except Exception as e:
            self.logger.error(f"加载主题设置失败: {e}")
            # 使用默认设置
            ModernTheme.apply_font_size('中等')
            ModernTheme.apply_theme('默认蓝色')

    def setup_window(self):
        """设置主窗口 - 优化布局"""
        self.root.title("文件自动分类工具 2.0 - 专业版")

        # 优化默认窗口尺寸，确保能显示全部功能界面
        self.root.geometry("1600x1000")  # 增大默认尺寸以显示全部功能
        self.root.minsize(1400, 800)     # 提高最小尺寸

        # 居中显示窗口
        self.center_window()

        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 获取窗口尺寸
        window_width = self.root.winfo_reqwidth()
        window_height = self.root.winfo_reqheight()

        # 如果窗口尺寸为0，使用默认值
        if window_width <= 1:
            window_width = 1600
        if window_height <= 1:
            window_height = 1000

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 确保窗口不会超出屏幕边界
        x = max(0, x)
        y = max(0, y)

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def setup_theme(self):
        """设置现代化主题"""
        self.style = ttk.Style()

        # 配置卡片样式
        self.style.configure(
            'Card.TFrame',
            background=ModernTheme.COLORS['list_bg'],
            relief='flat',
            borderwidth=1,
            bordercolor=ModernTheme.COLORS['scroll_track']
        )

        # 配置主按钮样式
        self.style.configure(
            'Primary.Modern.TButton',
            font=ModernTheme.FONTS['button_large'],
            foreground=ModernTheme.COLORS['text_white'],
            background=ModernTheme.COLORS['primary_start'],
            borderwidth=0,
            focuscolor='none',
            padding=(ModernTheme.SIZES['spacing_lg'], ModernTheme.SIZES['spacing_md'])
        )

        # 配置主按钮悬停样式
        self.style.configure(
            'Primary.Hover.Modern.TButton',
            font=ModernTheme.FONTS['button_large'],
            foreground=ModernTheme.COLORS['text_white'],
            background=ModernTheme.COLORS['primary_hover'],
            borderwidth=0,
            focuscolor='none',
            padding=(ModernTheme.SIZES['spacing_lg'], ModernTheme.SIZES['spacing_md'])
        )

        # 配置次要按钮样式
        self.style.configure(
            'Secondary.Modern.TButton',
            font=ModernTheme.FONTS['button'],
            foreground=ModernTheme.COLORS['text_primary'],
            background=ModernTheme.COLORS['nav_bg'],
            borderwidth=1,
            bordercolor=ModernTheme.COLORS['scroll_track'],
            focuscolor='none',
            padding=(ModernTheme.SIZES['spacing_md'], ModernTheme.SIZES['spacing_sm'])
        )

        # 配置Treeview样式
        self.style.configure(
            'Modern.Treeview',
            background=ModernTheme.COLORS['list_bg'],
            foreground=ModernTheme.COLORS['text_primary'],
            fieldbackground=ModernTheme.COLORS['list_bg'],
            borderwidth=0,
            font=ModernTheme.FONTS['content']
        )

        self.style.configure(
            'Modern.Treeview.Heading',
            background=ModernTheme.COLORS['nav_bg'],
            foreground=ModernTheme.COLORS['text_primary'],
            font=ModernTheme.FONTS['title'],
            borderwidth=0
        )

        # 配置目录树样式
        self.style.configure(
            'Tree.Modern.Treeview',
            background=ModernTheme.COLORS['tree_bg'],
            foreground=ModernTheme.COLORS['text_primary'],
            fieldbackground=ModernTheme.COLORS['tree_bg'],
            borderwidth=0,
            font=ModernTheme.FONTS['content']
        )

    def init_components(self):
        """初始化组件"""
        # 核心组件（config_manager已在__init__中初始化）
        self.file_processor = FileProcessor(self.config_manager)
        self.document_generator = DocumentGenerator(self.config_manager)

        # UI组件
        self.toast = ToastNotification(self.root)

        # 变量
        self.status_var = tk.StringVar(value="就绪")
        self.file_count_var = tk.StringVar(value="文件: 0")
        self.selected_count_var = tk.StringVar(value="已选: 0")

        # 文件类型变量
        self.txt_var = tk.BooleanVar(value=True)
        self.doc_var = tk.BooleanVar(value=True)
        self.pdf_var = tk.BooleanVar(value=True)
        self.excel_var = tk.BooleanVar(value=True)
        self.ppt_var = tk.BooleanVar(value=True)

    def create_layout(self):
        """创建界面布局"""
        # 主容器
        self.main_container = tk.Frame(self.root, bg=ModernTheme.COLORS['list_bg'])
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # 创建各个区域
        self.create_navigation_area()
        self.create_content_area()
        self.create_status_bar()

    def create_navigation_area(self):
        """创建顶部导航区域"""
        nav_frame = tk.Frame(
            self.main_container,
            bg=ModernTheme.COLORS['nav_bg'],
            height=60
        )
        nav_frame.pack(fill=tk.X, padx=0, pady=0)
        nav_frame.pack_propagate(False)

        # 左侧按钮组
        left_group = tk.Frame(nav_frame, bg=ModernTheme.COLORS['nav_bg'])
        left_group.pack(side=tk.LEFT, padx=ModernTheme.SIZES['spacing_lg'],
                       pady=ModernTheme.SIZES['spacing_md'])

        # 选择目录按钮
        self.btn_select_dir = ModernButton(
            left_group,
            text="选择目录",
            icon="📁",
            style_type="secondary",
            command=self.select_directory
        )
        self.btn_select_dir.pack(side=tk.LEFT, padx=(0, ModernTheme.SIZES['spacing_sm']))

        # 开始分类按钮（核心操作）
        self.btn_start_process = ModernButton(
            left_group,
            text="开始分类",
            icon="🚀",
            style_type="primary",
            width=ModernTheme.SIZES['button_large_width'] // 8,  # 转换为字符宽度
            command=self.start_processing
        )
        self.btn_start_process.pack(side=tk.LEFT, padx=(0, ModernTheme.SIZES['spacing_lg']))

        # 右侧控件组
        right_group = tk.Frame(nav_frame, bg=ModernTheme.COLORS['nav_bg'])
        right_group.pack(side=tk.RIGHT, padx=ModernTheme.SIZES['spacing_lg'],
                        pady=ModernTheme.SIZES['spacing_md'])

        # 搜索框
        search_frame = tk.Frame(right_group, bg=ModernTheme.COLORS['nav_bg'])
        search_frame.pack(side=tk.RIGHT, padx=(0, ModernTheme.SIZES['spacing_sm']))

        tk.Label(
            search_frame,
            text="🔍",
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_secondary'],
            font=ModernTheme.FONTS['content']
        ).pack(side=tk.LEFT)

        self.search_entry = tk.Entry(
            search_frame,
            width=ModernTheme.SIZES['search_width'] // 8,  # 转换为字符宽度
            font=ModernTheme.FONTS['content'],
            bg=ModernTheme.COLORS['list_bg'],
            fg=ModernTheme.COLORS['text_primary'],
            relief='flat',
            bd=1
        )
        self.search_entry.pack(side=tk.LEFT, padx=(ModernTheme.SIZES['spacing_xs'], 0))

        # 设置按钮
        self.btn_settings = ModernButton(
            right_group,
            text="",
            icon="⚙️",
            style_type="secondary",
            command=self.open_settings
        )
        self.btn_settings.pack(side=tk.RIGHT)

    def create_content_area(self):
        """创建主内容区域"""
        content_frame = tk.Frame(self.main_container, bg=ModernTheme.COLORS['list_bg'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=ModernTheme.SIZES['spacing_lg'],
                          pady=ModernTheme.SIZES['spacing_md'])

        # 创建三栏布局
        self.create_left_panel(content_frame)
        self.create_center_panel(content_frame)
        self.create_right_panel(content_frame)

    def create_left_panel(self, parent):
        """创建左侧目录树面板 - Windows资源管理器风格"""
        # 增加左侧面板宽度，确保能显示完整路径
        left_frame = tk.Frame(parent, bg=ModernTheme.COLORS['tree_bg'], width=380)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, ModernTheme.SIZES['spacing_md']))
        left_frame.pack_propagate(False)

        # 目录树卡片
        tree_card = ModernCard(left_frame, title="目录结构")
        tree_card.pack(fill=tk.BOTH, expand=True, padx=ModernTheme.SIZES['spacing_sm'],
                      pady=ModernTheme.SIZES['spacing_sm'])

        # 目录树工具栏
        tree_content = tree_card.get_content_frame()

        toolbar_frame = tk.Frame(tree_content, bg=ModernTheme.COLORS['tree_bg'], height=35)
        toolbar_frame.pack(fill=tk.X, pady=(0, ModernTheme.SIZES['spacing_xs']))
        toolbar_frame.pack_propagate(False)

        # 工具栏按钮
        self.btn_expand_all = tk.Button(
            toolbar_frame,
            text="展开全部",
            font=ModernTheme.FONTS['small'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=8,
            pady=2,
            command=self.expand_all_directories
        )
        self.btn_expand_all.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_collapse_all = tk.Button(
            toolbar_frame,
            text="折叠全部",
            font=ModernTheme.FONTS['small'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=8,
            pady=2,
            command=self.collapse_all_directories
        )
        self.btn_collapse_all.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_refresh_tree = tk.Button(
            toolbar_frame,
            text="🔄",
            font=ModernTheme.FONTS['small'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=8,
            pady=2,
            command=self.refresh_directory_tree
        )
        self.btn_refresh_tree.pack(side=tk.RIGHT)

        # 目录树
        self.directory_tree = ttk.Treeview(
            tree_content,
            style='Tree.Modern.Treeview',
            show='tree headings',
            columns=('path', 'type', 'size'),
            selectmode='browse'
        )

        # 配置列 - 所有列标题居中显示
        self.directory_tree.heading('#0', text='名称', anchor=tk.CENTER)
        self.directory_tree.heading('path', text='路径', anchor=tk.CENTER)
        self.directory_tree.heading('type', text='类型', anchor=tk.CENTER)
        self.directory_tree.heading('size', text='大小', anchor=tk.CENTER)

        self.directory_tree.column('#0', width=180, minwidth=120)
        self.directory_tree.column('path', width=0, minwidth=0, stretch=False)  # 隐藏路径列
        self.directory_tree.column('type', width=60, minwidth=50)
        self.directory_tree.column('size', width=80, minwidth=60)

        # 滚动条
        tree_scroll_y = ttk.Scrollbar(tree_content, orient=tk.VERTICAL, command=self.directory_tree.yview)
        tree_scroll_x = ttk.Scrollbar(tree_content, orient=tk.HORIZONTAL, command=self.directory_tree.xview)
        self.directory_tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)

        # 布局
        self.directory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 初始化空状态
        self.tree_empty_state = EmptyState.create(
            tree_content,
            "请选择目录开始浏览",
            "📁"
        )

        # 绑定右键菜单
        self.create_tree_context_menu()
        self.directory_tree.bind("<Button-3>", self.show_tree_context_menu)

        # 绑定展开事件
        self.directory_tree.bind("<<TreeviewOpen>>", self.on_tree_expand)
        self.directory_tree.bind("<<TreeviewClose>>", self.on_tree_collapse)

    def create_tree_context_menu(self):
        """创建目录树右键菜单"""
        self.tree_context_menu = tk.Menu(self.root, tearoff=0)
        self.tree_context_menu.add_command(label="📂 打开文件夹", command=self.open_folder_in_explorer)
        self.tree_context_menu.add_command(label="🔄 刷新", command=self.refresh_selected_directory)
        self.tree_context_menu.add_separator()
        self.tree_context_menu.add_command(label="📋 复制路径", command=self.copy_directory_path)
        self.tree_context_menu.add_command(label="📊 查看属性", command=self.show_directory_properties)

    def show_tree_context_menu(self, event):
        """显示目录树右键菜单"""
        # 选择右键点击的项目
        item = self.directory_tree.identify_row(event.y)
        if item:
            self.directory_tree.selection_set(item)
            self.tree_context_menu.post(event.x_root, event.y_root)

    def open_folder_in_explorer(self):
        """在资源管理器中打开文件所在的目录"""
        selection = self.directory_tree.selection()
        if selection:
            item = selection[0]
            values = self.directory_tree.item(item, 'values')
            if values:
                path = values[0]
                try:
                    if os.path.exists(path):
                        import subprocess
                        if os.path.isfile(path):
                            # 如果是文件，打开文件所在的目录并选中文件
                            subprocess.Popen(['explorer', '/select,', path])
                            self.toast.show("已在资源管理器中显示文件", "success")
                        else:
                            # 如果是目录，打开该目录的父目录并选中该目录
                            parent_dir = os.path.dirname(path)
                            if parent_dir and os.path.exists(parent_dir):
                                subprocess.Popen(['explorer', '/select,', path])
                                self.toast.show("已在资源管理器中显示目录", "success")
                            else:
                                # 如果没有父目录，直接打开该目录
                                subprocess.Popen(['explorer', path])
                                self.toast.show("已在资源管理器中打开目录", "success")
                    else:
                        self.toast.show("路径不存在", "error")
                except Exception as e:
                    self.logger.error(f"打开资源管理器失败: {str(e)}")
                    self.toast.show(f"打开失败: {str(e)}", "error")
            else:
                self.toast.show("无法获取路径信息", "error")

    def copy_directory_path(self):
        """复制目录路径到剪贴板"""
        selection = self.directory_tree.selection()
        if selection:
            item = selection[0]
            path = self.directory_tree.item(item, 'values')[0]
            self.root.clipboard_clear()
            self.root.clipboard_append(path)
            self.toast.show("路径已复制到剪贴板", "success")

    def show_directory_properties(self):
        """显示目录属性"""
        selection = self.directory_tree.selection()
        if selection:
            item = selection[0]
            path = self.directory_tree.item(item, 'values')[0]
            if os.path.exists(path):
                try:
                    stat = os.stat(path)
                    size = self._get_directory_size(path)
                    modified = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime))

                    properties_text = f"""
路径: {path}
类型: 文件夹
大小: {self._format_file_size(size)}
修改时间: {modified}
                    """

                    messagebox.showinfo("目录属性", properties_text.strip())
                except Exception as e:
                    self.toast.show(f"获取属性失败: {str(e)}", "error")

    def _get_directory_size(self, path):
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, IOError):
                        continue
        except (OSError, IOError):
            pass
        return total_size

    def expand_all_directories(self):
        """展开所有目录"""
        def expand_item(item):
            self.directory_tree.item(item, open=True)
            for child in self.directory_tree.get_children(item):
                expand_item(child)

        for item in self.directory_tree.get_children():
            expand_item(item)
        self.toast.show("已展开所有目录", "success")

    def collapse_all_directories(self):
        """折叠所有目录"""
        def collapse_item(item):
            self.directory_tree.item(item, open=False)
            for child in self.directory_tree.get_children(item):
                collapse_item(child)

        for item in self.directory_tree.get_children():
            collapse_item(item)
        self.toast.show("已折叠所有目录", "success")

    def refresh_selected_directory(self):
        """刷新选中的目录"""
        selection = self.directory_tree.selection()
        if selection:
            item = selection[0]
            path = self.directory_tree.item(item, 'values')[0]
            self._refresh_directory_item(item, path)
            self.toast.show("目录已刷新", "success")

    def on_tree_expand(self, event):
        """目录树展开事件"""
        # 获取被展开的项目
        item = self.directory_tree.focus()
        if not item:
            return

        # 检查是否已经加载过内容
        children = self.directory_tree.get_children(item)
        if children and self.directory_tree.item(children[0], 'text') == "加载中...":
            path = self.directory_tree.item(item, 'values')[0]
            # 延迟加载子目录
            self._load_subdirectories(item, path)

    def on_tree_collapse(self, event):
        """目录树折叠事件"""
        # 可以在这里添加折叠时的逻辑
        pass

    def create_center_panel(self, parent):
        """创建中央文件列表面板"""
        center_frame = tk.Frame(parent, bg=ModernTheme.COLORS['list_bg'])
        center_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True,
                         padx=(0, ModernTheme.SIZES['spacing_md']))

        # 文件列表卡片
        list_card = ModernCard(center_frame, title="文件列表")
        list_card.pack(fill=tk.BOTH, expand=True)

        # 文件列表
        list_content = list_card.get_content_frame()

        # 文件列表工具栏
        list_toolbar = tk.Frame(list_content, bg=ModernTheme.COLORS['list_bg'])
        list_toolbar.pack(fill=tk.X, pady=(0, ModernTheme.SIZES['spacing_sm']))

        # 文件类型过滤器
        filter_frame = tk.Frame(list_toolbar, bg=ModernTheme.COLORS['list_bg'])
        filter_frame.pack(side=tk.LEFT)

        tk.Label(
            filter_frame,
            text="文件类型:",
            font=ModernTheme.FONTS['content'],
            bg=ModernTheme.COLORS['list_bg'],
            fg=ModernTheme.COLORS['text_secondary']
        ).pack(side=tk.LEFT, padx=(0, ModernTheme.SIZES['spacing_sm']))

        # 文件类型复选框
        self.create_file_type_checkboxes(filter_frame)

        # 文件操作按钮
        action_frame = tk.Frame(list_toolbar, bg=ModernTheme.COLORS['list_bg'])
        action_frame.pack(side=tk.RIGHT)

        self.btn_select_all = ModernButton(
            action_frame,
            text="全选",
            icon="☑️",
            style_type="secondary",
            command=self.toggle_select_all_files
        )
        self.btn_select_all.pack(side=tk.LEFT, padx=(0, ModernTheme.SIZES['spacing_xs']))

        self.btn_select_none = ModernButton(
            action_frame,
            text="全不选",
            icon="☐",
            style_type="secondary",
            command=self.deselect_all_files
        )
        self.btn_select_none.pack(side=tk.LEFT, padx=(0, ModernTheme.SIZES['spacing_xs']))

        self.btn_invert_selection = ModernButton(
            action_frame,
            text="反选",
            icon="🔄",
            style_type="secondary",
            command=self.invert_file_selection
        )
        self.btn_invert_selection.pack(side=tk.LEFT, padx=(0, ModernTheme.SIZES['spacing_xs']))

        self.btn_preview = ModernButton(
            action_frame,
            text="预览",
            icon="👁️",
            style_type="secondary",
            command=self.preview_selected_file
        )
        self.btn_preview.pack(side=tk.LEFT)

        # 文件列表表格 - 添加复选框支持
        self.file_tree = ttk.Treeview(
            list_content,
            style='Modern.Treeview',
            columns=('selected', 'size', 'modified', 'type', 'category', 'path'),
            show='tree headings',
            selectmode='extended'
        )

        # 配置列 - 所有列标题居中显示
        self.file_tree.heading('#0', text='文件名', anchor=tk.CENTER)
        self.file_tree.heading('selected', text='☑️', anchor=tk.CENTER)
        self.file_tree.heading('size', text='大小', anchor=tk.CENTER)
        self.file_tree.heading('modified', text='修改时间', anchor=tk.CENTER)
        self.file_tree.heading('type', text='类型', anchor=tk.CENTER)
        self.file_tree.heading('category', text='分类', anchor=tk.CENTER)

        self.file_tree.column('#0', width=280, minwidth=180)
        self.file_tree.column('selected', width=40, minwidth=40, anchor=tk.CENTER)
        self.file_tree.column('size', width=80, minwidth=60)
        self.file_tree.column('modified', width=150, minwidth=120)
        self.file_tree.column('type', width=80, minwidth=60)
        self.file_tree.column('category', width=120, minwidth=100)
        self.file_tree.column('path', width=0, minwidth=0)  # 隐藏列，用于存储文件路径
        self.file_tree.heading('path', text='')  # 隐藏列标题

        # 文件选择状态跟踪
        self.file_selection_state = {}  # 文件路径 -> 是否选中
        # 文件路径到item ID的映射
        self.file_path_to_item = {}  # 文件路径 -> TreeView item ID

        # 滚动条
        file_scroll_y = ttk.Scrollbar(list_content, orient=tk.VERTICAL, command=self.file_tree.yview)
        file_scroll_x = ttk.Scrollbar(list_content, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=file_scroll_y.set, xscrollcommand=file_scroll_x.set)

        # 布局
        self.file_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        file_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        file_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 加载指示器
        self.file_loading = LoadingIndicator(list_content)

        # 初始化空状态
        self.file_empty_state = EmptyState.create(
            list_content,
            "未检测到文件，请先选择目录",
            "📁"
        )

    def create_file_type_checkboxes(self, parent):
        """创建文件类型复选框"""
        file_types = [
            (self.txt_var, "TXT", "📄"),
            (self.doc_var, "DOC", "📝"),
            (self.pdf_var, "PDF", "📋"),
            (self.excel_var, "XLS", "📊"),
            (self.ppt_var, "PPT", "📈")
        ]

        for var, text, icon in file_types:
            checkbox_frame = tk.Frame(parent, bg=ModernTheme.COLORS['list_bg'])
            checkbox_frame.pack(side=tk.LEFT, padx=(0, ModernTheme.SIZES['spacing_md']))

            # 自定义复选框
            checkbox = tk.Label(
                checkbox_frame,
                text=f"✅ {icon} {text}",
                font=ModernTheme.FONTS['content'],
                bg=ModernTheme.COLORS['list_bg'],
                fg=ModernTheme.COLORS['text_primary'],
                cursor='hand2'
            )
            checkbox.pack()

            # 绑定点击事件
            checkbox.bind('<Button-1>', lambda event, v=var, cb=checkbox, t=text, i=icon:
                         self.toggle_file_type(v, cb, t, i))

            # 存储复选框引用
            setattr(self, f'{text.lower()}_checkbox', checkbox)

    def toggle_file_type(self, var, checkbox, text, icon):
        """切换文件类型选择"""
        var.set(not var.get())
        symbol = "✅" if var.get() else "☐"
        checkbox.configure(text=f"{symbol} {icon} {text}")

        # 刷新文件列表
        self.refresh_file_list()

    def create_right_panel(self, parent):
        """创建右侧分类配置面板 - 优化宽度"""
        # 增加右侧面板宽度，确保能显示所有控件和按钮
        right_frame = tk.Frame(parent, bg=ModernTheme.COLORS['config_bg'], width=400)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)
        right_frame.pack_propagate(False)

        # 分类配置卡片
        config_card = ModernCard(right_frame, title="分类配置")
        config_card.pack(fill=tk.BOTH, expand=True, padx=ModernTheme.SIZES['spacing_sm'],
                        pady=ModernTheme.SIZES['spacing_sm'])

        config_content = config_card.get_content_frame()

        # 分类规则列表标题
        rules_title_frame = tk.Frame(config_content, bg=ModernTheme.COLORS['list_bg'])
        rules_title_frame.pack(fill=tk.X, pady=(0, ModernTheme.SIZES['spacing_sm']))

        rules_title_label = tk.Label(
            rules_title_frame,
            text="当前分类规则",
            font=ModernTheme.FONTS['subtitle'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        rules_title_label.pack(side=tk.LEFT)

        # 刷新规则按钮
        refresh_rules_btn = tk.Button(
            rules_title_frame,
            text="🔄",
            font=ModernTheme.FONTS['small'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=8,
            pady=2,
            command=self.refresh_rules_display
        )
        refresh_rules_btn.pack(side=tk.RIGHT)

        # 分类规则列表
        rules_frame = tk.Frame(config_content, bg=ModernTheme.COLORS['list_bg'])
        rules_frame.pack(fill=tk.BOTH, expand=True, pady=(0, ModernTheme.SIZES['spacing_md']))

        self.rules_tree = ttk.Treeview(
            rules_frame,
            style='Modern.Treeview',
            columns=('keywords', 'match_type', 'priority'),
            show='tree headings',
            height=8
        )

        # 配置列 - 所有列标题居中显示
        self.rules_tree.heading('#0', text='分类名称', anchor=tk.CENTER)
        self.rules_tree.heading('keywords', text='关键词', anchor=tk.CENTER)
        self.rules_tree.heading('match_type', text='匹配方式', anchor=tk.CENTER)
        self.rules_tree.heading('priority', text='优先级', anchor=tk.CENTER)

        self.rules_tree.column('#0', width=100, minwidth=80)
        self.rules_tree.column('keywords', width=150, minwidth=120)
        self.rules_tree.column('match_type', width=80, minwidth=60)
        self.rules_tree.column('priority', width=60, minwidth=50)

        rules_scroll = ttk.Scrollbar(rules_frame, orient=tk.VERTICAL, command=self.rules_tree.yview)
        self.rules_tree.configure(yscrollcommand=rules_scroll.set)

        self.rules_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        rules_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 规则统计信息
        self.rules_info_var = tk.StringVar(value="规则数量: 0")
        rules_info_label = tk.Label(
            config_content,
            textvariable=self.rules_info_var,
            font=ModernTheme.FONTS['small'],
            fg=ModernTheme.COLORS['text_secondary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        rules_info_label.pack(pady=(ModernTheme.SIZES['spacing_xs'], 0))

        # 规则管理按钮
        rules_button_frame = tk.Frame(config_content, bg=ModernTheme.COLORS['list_bg'])
        rules_button_frame.pack(fill=tk.X, pady=(ModernTheme.SIZES['spacing_sm'], 0))

        self.btn_manage_rules = ModernButton(
            rules_button_frame,
            text="规则管理",
            icon="⚙️",
            style_type="secondary",
            command=self.open_rule_manager
        )
        self.btn_manage_rules.pack(side=tk.LEFT)

        self.btn_add_rule = ModernButton(
            rules_button_frame,
            text="添加规则",
            icon="➕",
            style_type="primary",
            command=self.add_new_rule
        )
        self.btn_add_rule.pack(side=tk.LEFT, padx=(ModernTheme.SIZES['spacing_sm'], 0))

        # 文章汇总按钮（添加规则右边）
        self.btn_generate_summary = ModernButton(
            rules_button_frame,
            text="文章汇总",
            icon="📄",
            style_type="secondary",
            command=self.generate_summary
        )
        self.btn_generate_summary.pack(side=tk.LEFT, padx=(ModernTheme.SIZES['spacing_sm'], 0))

        # 初始化规则显示
        self.refresh_rules_display()

    def refresh_rules_display(self):
        """刷新分类规则显示"""
        # 清空现有项目
        for item in self.rules_tree.get_children():
            self.rules_tree.delete(item)

        try:
            categories = self.config_manager.get_categories()

            # 按优先级排序显示
            sorted_categories = sorted(categories, key=lambda x: x.get('priority', 0), reverse=True)

            for category in sorted_categories:
                name = category.get('name', '未命名')
                keywords = category.get('keywords', [])
                match_type = category.get('match_type', 'contains')
                priority = category.get('priority', 1)

                # 格式化关键词显示
                keywords_text = ', '.join(keywords[:3])  # 只显示前3个关键词
                if len(keywords) > 3:
                    keywords_text += f" (+{len(keywords)-3})"

                # 格式化匹配方式
                match_type_text = {
                    'contains': '包含',
                    'startswith': '开头',
                    'default': '默认'
                }.get(match_type, '包含')

                # 选择图标
                if match_type == 'default':
                    icon = "🏠"
                elif match_type == 'startswith':
                    icon = "🔤"
                else:
                    icon = "🔍"

                self.rules_tree.insert(
                    '', 'end',
                    text=f"{icon} {name}",
                    values=(keywords_text, match_type_text, priority)
                )

            # 更新统计信息
            self.rules_info_var.set(f"规则数量: {len(categories)}")

            if not categories:
                # 显示空状态提示
                self.rules_tree.insert(
                    '', 'end',
                    text="📝 暂无分类规则",
                    values=("请点击设置添加分类规则", "", "")
                )

        except Exception as e:
            self.logger.error(f"刷新规则显示失败: {str(e)}")
            self.toast.show(f"刷新规则失败: {str(e)}", "error")




    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(
            self.main_container,
            bg=ModernTheme.COLORS['nav_bg'],
            height=30
        )
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)

        # 左侧状态信息
        left_status = tk.Frame(status_frame, bg=ModernTheme.COLORS['nav_bg'])
        left_status.pack(side=tk.LEFT, padx=ModernTheme.SIZES['spacing_md'])

        self.status_label = tk.Label(
            left_status,
            textvariable=self.status_var,
            font=ModernTheme.FONTS['auxiliary'],
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_secondary']
        )
        self.status_label.pack(side=tk.LEFT)

        # 右侧计数信息
        right_status = tk.Frame(status_frame, bg=ModernTheme.COLORS['nav_bg'])
        right_status.pack(side=tk.RIGHT, padx=ModernTheme.SIZES['spacing_md'])

        self.file_count_label = tk.Label(
            right_status,
            textvariable=self.file_count_var,
            font=ModernTheme.FONTS['auxiliary'],
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_secondary']
        )
        self.file_count_label.pack(side=tk.RIGHT, padx=(ModernTheme.SIZES['spacing_md'], 0))

        self.selected_count_label = tk.Label(
            right_status,
            textvariable=self.selected_count_var,
            font=ModernTheme.FONTS['auxiliary'],
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_secondary']
        )
        self.selected_count_label.pack(side=tk.RIGHT)

    def bind_events(self):
        """绑定事件"""
        # 文件列表事件
        self.file_tree.bind('<<TreeviewSelect>>', self.on_file_select)
        self.file_tree.bind('<Double-1>', self.on_file_double_click)
        self.file_tree.bind("<Button-1>", self.on_file_click)

        # 目录树事件
        self.directory_tree.bind('<<TreeviewSelect>>', self.on_directory_select)

        # 搜索事件
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        self.search_entry.bind('<Return>', self.perform_search)

        # 快捷键
        self.root.bind('<Control-a>', lambda event: self.toggle_select_all_files())
        self.root.bind('<F5>', lambda event: self.refresh_all())
        self.root.bind('<Control-o>', lambda event: self.select_directory())

        # 窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    # 事件处理方法
    def select_directory(self):
        """选择目录"""
        directory = filedialog.askdirectory(title="选择待分类的目录")
        if directory:
            self.current_directory = directory
            self.status_var.set(f"已选择目录: {os.path.basename(directory)}")
            self.refresh_directory_tree()
            self.refresh_file_list()
            self.logger.info(f"选择目录: {directory}")

    def start_processing(self):
        """开始文件分类处理"""
        if not self.current_directory:
            self.toast.show("请先选择要分类的目录", "error")
            return

        if self.is_processing:
            return

        # 设置处理状态
        self.is_processing = True
        self.btn_start_process.set_loading(True)
        self.status_var.set("正在分类文件...")

        # 在后台线程中处理
        threading.Thread(target=self._process_files, daemon=True).start()

    def _process_files(self):
        """后台处理文件（在线程中运行）- 实现真正的文件分类和内容提取"""
        try:
            # 获取选中的文件类型
            selected_types = []
            if self.txt_var.get():
                selected_types.extend(['.txt'])
            if self.doc_var.get():
                selected_types.extend(['.doc', '.docx'])
            if self.pdf_var.get():
                selected_types.extend(['.pdf'])
            if self.excel_var.get():
                selected_types.extend(['.xls', '.xlsx'])
            if self.ppt_var.get():
                selected_types.extend(['.ppt', '.pptx'])

            if not selected_types:
                self.root.after(0, lambda: self.toast.show("请至少选择一种文件类型", "error"))
                return

            # 初始化分类文件映射
            self.category_files = {}
            self.processed_titles = set()

            # 获取目标路径
            config = self.config_manager.get_config()
            target_path = config.get('target_path', './已分类')

            # 获取被勾选的文件
            all_files = []
            for file_path, is_selected in self.file_selection_state.items():
                if is_selected and os.path.isfile(file_path):
                    file_ext = os.path.splitext(file_path)[1].lower()
                    if file_ext in selected_types:
                        all_files.append(file_path)

            total_files = len(all_files)
            processed_count = 0

            for file_path in all_files:
                if not self.is_processing:  # 检查是否需要取消
                    break

                file_name = os.path.basename(file_path)

                # 获取文件分类
                category = self.get_category(file_name)

                # 提取文件内容（用于汇总）
                title, content = self.extract_file_content(file_path)

                # 基于标题去重检查
                if title not in self.processed_titles:
                    # 添加到分类集合
                    if category not in self.category_files:
                        self.category_files[category] = []
                    self.category_files[category].append((title, content))
                    self.processed_titles.add(title)

                    # 文件分类复制
                    dest_dir = os.path.join(target_path, category)
                    if not os.path.exists(dest_dir):
                        os.makedirs(dest_dir)

                    dest_path = os.path.join(dest_dir, file_name)

                    # 如果目标文件已存在，添加序号
                    counter = 1
                    original_dest_path = dest_path
                    while os.path.exists(dest_path):
                        name, ext = os.path.splitext(original_dest_path)
                        dest_path = f"{name}_{counter}{ext}"
                        counter += 1

                    # 复制文件
                    import shutil
                    shutil.copy2(file_path, dest_path)

                processed_count += 1

                # 更新状态
                self.root.after(0, lambda c=processed_count, t=total_files:
                               self.status_var.set(f"正在处理... ({c}/{t})"))

            # 处理完成
            self.root.after(0, self._processing_completed, processed_count)

        except Exception as e:
            self.logger.error(f"文件处理失败: {str(e)}")
            self.root.after(0, lambda: self.toast.show(f"处理失败: {str(e)}", "error"))
        finally:
            # 重置状态
            self.root.after(0, self._reset_processing_state)

    def _processing_completed(self, count):
        """处理完成回调"""
        self.toast.show(f"分类完成（共 {count} 个文件）", "success")
        self.refresh_file_list()
        self.refresh_directory_tree()

    def _reset_processing_state(self):
        """重置处理状态"""
        self.is_processing = False
        self.btn_start_process.set_loading(False)
        self.status_var.set("就绪")

    def refresh_directory_tree(self):
        """刷新目录树 - Windows资源管理器风格"""
        # 清空现有项目
        for item in self.directory_tree.get_children():
            self.directory_tree.delete(item)

        if not self.current_directory or not os.path.exists(self.current_directory):
            self.tree_empty_state.place(relx=0.5, rely=0.5, anchor=tk.CENTER)
            return

        self.tree_empty_state.place_forget()

        try:
            # 添加根目录
            root_name = os.path.basename(self.current_directory) or self.current_directory
            root_size = self._get_directory_size(self.current_directory)

            root_item = self.directory_tree.insert(
                '', 'end',
                text=f"📁 {root_name}",
                values=(self.current_directory, "文件夹", self._format_file_size(root_size)),
                open=True
            )

            # 递归添加子项目
            self._add_directory_items(root_item, self.current_directory, max_depth=2)

        except Exception as e:
            self.logger.error(f"刷新目录树失败: {str(e)}")
            self.toast.show(f"刷新目录树失败: {str(e)}", "error")

    def _add_directory_items(self, parent_item, directory_path, current_depth=0, max_depth=3):
        """递归添加目录项目"""
        if current_depth >= max_depth:
            return

        try:
            items = []

            # 获取目录内容
            for item_name in os.listdir(directory_path):
                item_path = os.path.join(directory_path, item_name)

                try:
                    if os.path.isdir(item_path):
                        # 目录
                        dir_size = self._get_directory_size(item_path) if current_depth < 1 else 0
                        items.append(('dir', item_name, item_path, dir_size))
                    elif os.path.isfile(item_path):
                        # 文件
                        file_size = os.path.getsize(item_path)
                        items.append(('file', item_name, item_path, file_size))
                except (OSError, IOError):
                    # 跳过无法访问的项目
                    continue

            # 排序：目录在前，文件在后，按名称排序
            items.sort(key=lambda x: (x[0] == 'file', x[1].lower()))

            # 检查是否已经存在相同的项目，避免重复添加
            existing_items = set()
            for child in self.directory_tree.get_children(parent_item):
                child_values = self.directory_tree.item(child, 'values')
                if child_values:
                    existing_items.add(child_values[0])  # 添加路径到集合

            # 添加到树中
            for item_type, item_name, item_path, item_size in items:
                # 跳过已存在的项目
                if item_path in existing_items:
                    continue

                if item_type == 'dir':
                    # 添加目录
                    dir_item = self.directory_tree.insert(
                        parent_item, 'end',
                        text=f"📁 {item_name}",
                        values=(item_path, "文件夹", self._format_file_size(item_size))
                    )

                    # 检查是否有子目录，如果有则添加占位符
                    if self._has_subdirectories(item_path):
                        self.directory_tree.insert(dir_item, 'end', text="加载中...")

                else:
                    # 添加文件
                    file_icon = self._get_file_icon(os.path.splitext(item_name)[1].upper())
                    file_type = os.path.splitext(item_name)[1].upper() or "文件"

                    self.directory_tree.insert(
                        parent_item, 'end',
                        text=f"{file_icon} {item_name}",
                        values=(item_path, file_type, self._format_file_size(item_size))
                    )

        except Exception as e:
            self.logger.error(f"添加目录项目失败: {str(e)}")

    def _has_subdirectories(self, directory_path):
        """检查目录是否有子目录"""
        try:
            for item in os.listdir(directory_path):
                if os.path.isdir(os.path.join(directory_path, item)):
                    return True
        except (OSError, IOError):
            pass
        return False

    def _load_subdirectories(self, parent_item, directory_path):
        """延迟加载子目录"""
        # 检查是否已经加载过实际内容
        children = self.directory_tree.get_children(parent_item)
        has_placeholder = False
        has_real_content = False

        for child in children:
            child_text = self.directory_tree.item(child, 'text')
            if child_text == "加载中...":
                has_placeholder = True
            else:
                has_real_content = True

        # 如果已经有实际内容，不重复加载
        if has_real_content and not has_placeholder:
            return

        # 删除占位符
        for child in children:
            if self.directory_tree.item(child, 'text') == "加载中...":
                self.directory_tree.delete(child)

        # 加载实际内容
        self._add_directory_items(parent_item, directory_path, current_depth=1, max_depth=2)

    def _refresh_directory_item(self, item, path):
        """刷新特定目录项目"""
        # 删除所有子项目
        for child in self.directory_tree.get_children(item):
            self.directory_tree.delete(child)

        # 重新加载
        self._add_directory_items(item, path, current_depth=0, max_depth=1)

    def refresh_file_list(self):
        """刷新文件列表"""
        # 清空现有项目
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # 清空文件选择状态
        self.file_selection_state.clear()

        if not self.current_directory or not os.path.exists(self.current_directory):
            self.file_empty_state.place(relx=0.5, rely=0.5, anchor=tk.CENTER)
            self.file_count_var.set("文件: 0")
            self.selected_count_var.set("已选: 0")
            return

        self.file_empty_state.place_forget()

        try:
            # 获取文件列表
            files = []
            try:
                for item in os.listdir(self.current_directory):
                    item_path = os.path.join(self.current_directory, item)
                    try:
                        if os.path.isfile(item_path):
                            files.append(item_path)
                    except (OSError, IOError, PermissionError):
                        # 跳过无法访问的文件
                        continue
            except (OSError, IOError, PermissionError) as e:
                self.logger.error(f"无法读取目录 {self.current_directory}: {str(e)}")
                self.toast.show(f"无法读取目录: {str(e)}", "error")
                return

            # 过滤文件类型
            filtered_files = self._filter_files_by_type(files)

            # 记录调试信息
            self.logger.info(f"目录 {self.current_directory} 中找到 {len(files)} 个文件，过滤后 {len(filtered_files)} 个文件")

            # 创建文件路径到item ID的映射
            self.file_path_to_item = {}

            # 添加到列表
            for file_path in filtered_files:
                try:
                    file_name = os.path.basename(file_path)
                    file_size = self._format_file_size(os.path.getsize(file_path))
                    file_modified = time.strftime('%Y-%m-%d %H:%M',
                                                time.localtime(os.path.getmtime(file_path)))
                    file_ext = os.path.splitext(file_name)[1].upper()
                    file_category = self._get_file_category(file_name)

                    # 选择文件图标
                    icon = self._get_file_icon(file_ext)

                    # 初始化选择状态 - 默认所有文件都被勾选
                    self.file_selection_state[file_path] = True

                    # 复选框状态显示 - 默认显示为已选中
                    checkbox_icon = "✅"

                    item_id = self.file_tree.insert(
                        '', 'end',
                        text=f"{icon} {file_name}",
                        values=(checkbox_icon, file_size, file_modified, file_ext, file_category, file_path)
                    )

                    # 存储文件路径到item的映射
                    self.file_path_to_item[file_path] = item_id

                except Exception as e:
                    self.logger.error(f"添加文件 {file_path} 到列表失败: {str(e)}")
                    continue

            self.file_count_var.set(f"文件: {len(filtered_files)}")
            self.selected_count_var.set(f"已选: {len(filtered_files)}/{len(filtered_files)}")

            # 如果没有文件，显示空状态
            if len(filtered_files) == 0:
                self.file_empty_state.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        except Exception as e:
            self.logger.error(f"刷新文件列表失败: {str(e)}")
            self.toast.show(f"刷新文件列表失败: {str(e)}", "error")

    def _filter_files_by_type(self, files):
        """根据选中的文件类型过滤文件"""
        # 检查是否有任何文件类型被选中
        any_selected = any([self.txt_var.get(), self.doc_var.get(), self.pdf_var.get(),
                           self.excel_var.get(), self.ppt_var.get()])

        # 如果没有任何类型被选中，显示所有文件
        if not any_selected:
            return files

        filtered = []
        for file_path in files:
            ext = os.path.splitext(file_path)[1].lower()

            # 检查文件是否匹配选中的类型
            if (self.txt_var.get() and ext == '.txt') or \
               (self.doc_var.get() and ext in ['.doc', '.docx']) or \
               (self.pdf_var.get() and ext == '.pdf') or \
               (self.excel_var.get() and ext in ['.xls', '.xlsx']) or \
               (self.ppt_var.get() and ext in ['.ppt', '.pptx']):
                filtered.append(file_path)
            # 如果文件类型不在预定义列表中，但有类型被选中，也显示该文件
            elif any_selected and ext not in ['.txt', '.doc', '.docx', '.pdf', '.xls', '.xlsx', '.ppt', '.pptx']:
                filtered.append(file_path)

        return filtered

    def _format_file_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    def _get_file_icon(self, ext):
        """获取文件图标"""
        icon_map = {
            '.TXT': '📄',
            '.DOC': '📝', '.DOCX': '📝',
            '.PDF': '📋',
            '.XLS': '📊', '.XLSX': '📊',
            '.PPT': '📈', '.PPTX': '📈',
        }
        return icon_map.get(ext, '📄')

    def _get_file_category(self, filename):
        """获取文件分类"""
        return self.get_category(filename)

    def refresh_all(self):
        """刷新所有数据"""
        self.status_var.set("正在刷新...")
        self.refresh_directory_tree()
        self.refresh_file_list()
        self.status_var.set("刷新完成")
        self.toast.show("界面刷新完成", "success")

    # 事件处理方法
    def on_file_select(self, event):
        """文件选择事件"""
        selected = self.file_tree.selection()
        self.selected_count_var.set(f"已选: {len(selected)}")

        # 更新预览按钮状态
        if hasattr(self, 'btn_preview'):
            if selected:
                self.btn_preview.configure(state='normal')
            else:
                self.btn_preview.configure(state='disabled')

    def on_file_double_click(self, event):
        """文件双击事件"""
        self.preview_selected_file()

    def on_directory_select(self, event):
        """目录选择事件"""
        selection = self.directory_tree.selection()
        if selection:
            item = selection[0]
            path = self.directory_tree.item(item, 'values')[0]
            if path != self.current_directory:
                self.current_directory = path
                self.file_loading.show("正在加载文件...")
                # 延迟刷新以显示加载效果
                self.root.after(100, self._delayed_refresh)

    def _delayed_refresh(self):
        """延迟刷新文件列表"""
        self.refresh_file_list()
        self.file_loading.hide()

    def on_search_change(self, event):
        """搜索内容变化"""
        # 实时搜索功能
        search_text = self.search_entry.get().lower()
        if not search_text:
            self.refresh_file_list()
            return

        # 过滤显示
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # 重新添加匹配的文件
        # 这里可以实现更复杂的搜索逻辑

    def perform_search(self, event=None):
        """执行搜索"""
        search_text = self.search_entry.get()
        if search_text:
            self.status_var.set(f"搜索: {search_text}")
        else:
            self.refresh_file_list()

    def on_file_click(self, event):
        """文件列表点击事件"""
        # 获取点击的位置
        region = self.file_tree.identify_region(event.x, event.y)
        if region == "cell":
            # 获取点击的列
            column = self.file_tree.identify_column(event.x)
            if column == "#1":  # 复选框列 (selected列)
                # 获取点击的项目
                item = self.file_tree.identify_row(event.y)
                if item:
                    self.toggle_file_selection(item)

    def toggle_file_selection(self, item):
        """切换文件选择状态"""
        try:
            # 获取文件路径
            file_path = self.file_tree.set(item, 'path')
            if not file_path:
                self.logger.warning(f"无法找到item {item} 对应的文件路径")
                return

            # 切换选择状态
            current_state = self.file_selection_state.get(file_path, False)
            new_state = not current_state
            self.file_selection_state[file_path] = new_state

            # 更新复选框显示
            checkbox_icon = "✅" if new_state else "☐"
            values = list(self.file_tree.item(item, 'values'))
            if values:
                values[0] = checkbox_icon  # 更新复选框列
                self.file_tree.item(item, values=values)

            # 更新选择计数
            self.update_selection_count()

        except Exception as e:
            self.logger.error(f"切换文件选择状态失败: {str(e)}")

    def update_selection_count(self):
        """更新选择计数显示"""
        selected_count = sum(1 for selected in self.file_selection_state.values() if selected)
        total_count = len(self.file_selection_state)
        self.selected_count_var.set(f"已选: {selected_count}/{total_count}")

    def toggle_select_all_files(self):
        """切换全选状态"""
        # 检查当前是否全部选中
        selected_count = sum(1 for selected in self.file_selection_state.values() if selected)
        total_count = len(self.file_selection_state)

        if selected_count == total_count and total_count > 0:
            # 当前全选，执行全不选
            self.deselect_all_files()
        else:
            # 执行全选
            self.select_all_files()

    def select_all_files(self):
        """全选文件"""
        count = 0
        for item in self.file_tree.get_children():
            file_path = self.file_tree.set(item, 'path')
            if file_path:
                self.file_selection_state[file_path] = True
                # 更新显示
                values = list(self.file_tree.item(item, 'values'))
                if values:
                    values[0] = "✅"
                    self.file_tree.item(item, values=values)
                    count += 1

        self.update_selection_count()
        self.toast.show(f"已选择 {count} 个文件", "success")

    def deselect_all_files(self):
        """全不选文件"""
        count = 0
        for item in self.file_tree.get_children():
            file_path = self.file_tree.set(item, 'path')
            if file_path:
                self.file_selection_state[file_path] = False
                # 更新显示
                values = list(self.file_tree.item(item, 'values'))
                if values:
                    values[0] = "☐"
                    self.file_tree.item(item, values=values)
                    count += 1

        self.update_selection_count()
        self.toast.show(f"已取消选择 {count} 个文件", "success")

    def invert_file_selection(self):
        """反选文件"""
        count = 0
        for item in self.file_tree.get_children():
            file_path = self.file_tree.set(item, 'path')
            if file_path:
                current_state = self.file_selection_state.get(file_path, False)
                new_state = not current_state
                self.file_selection_state[file_path] = new_state
                # 更新显示
                values = list(self.file_tree.item(item, 'values'))
                if values:
                    values[0] = "✅" if new_state else "☐"
                    self.file_tree.item(item, values=values)
                    count += 1

        self.update_selection_count()
        self.toast.show(f"已反选 {count} 个文件", "success")

    def get_selected_files(self):
        """获取选中的文件列表"""
        return [file_path for file_path, selected in self.file_selection_state.items() if selected]

    def preview_selected_file(self):
        """预览选中文件"""
        selection = self.file_tree.selection()
        if not selection:
            self.toast.show("请先选择要预览的文件", "error")
            return

        try:
            # 获取选中的文件路径
            selected_item = selection[0]
            file_path = self.file_tree.set(selected_item, 'path')

            if not file_path or not os.path.exists(file_path):
                self.toast.show("找不到选中的文件", "error")
                return

            # 创建预览窗口
            self._show_file_preview(file_path)

        except Exception as e:
            self.logger.error(f"预览文件失败: {str(e)}")
            self.toast.show(f"预览文件失败: {str(e)}", "error")

    def _show_file_preview(self, file_path):
        """显示文件预览窗口"""
        try:
            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title(f"文件预览 - {os.path.basename(file_path)}")
            preview_window.geometry("800x600")
            preview_window.transient(self.root)
            preview_window.grab_set()

            # 创建文本框显示内容
            text_frame = tk.Frame(preview_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 读取文件内容
            file_ext = os.path.splitext(file_path)[1].lower()
            content = self._extract_file_content(file_path, file_ext)

            # 显示内容
            text_widget.insert(tk.END, content)
            text_widget.config(state=tk.DISABLED)

            # 添加关闭按钮
            button_frame = tk.Frame(preview_window)
            button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

            close_btn = tk.Button(button_frame, text="关闭", command=preview_window.destroy)
            close_btn.pack(side=tk.RIGHT)

        except Exception as e:
            self.logger.error(f"显示文件预览失败: {str(e)}")
            self.toast.show(f"显示文件预览失败: {str(e)}", "error")

    def _extract_file_content(self, file_path, file_ext):
        """提取文件内容"""
        try:
            if file_ext in ['.txt', '.log', '.md', '.py', '.js', '.html', '.css', '.json', '.xml']:
                # 文本文件
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return f.read()
            elif file_ext in ['.doc', '.docx']:
                # Word文档
                try:
                    import docx
                    doc = docx.Document(file_path)
                    content = []
                    for paragraph in doc.paragraphs:
                        content.append(paragraph.text)
                    return '\n'.join(content)
                except ImportError:
                    return "需要安装 python-docx 库来预览Word文档"
                except Exception:
                    return "无法读取Word文档内容"
            elif file_ext == '.pdf':
                # PDF文件
                try:
                    import PyPDF2
                    with open(file_path, 'rb') as f:
                        reader = PyPDF2.PdfReader(f)
                        content = []
                        for page in reader.pages:
                            content.append(page.extract_text())
                        return '\n'.join(content)
                except ImportError:
                    return "需要安装 PyPDF2 库来预览PDF文档"
                except Exception:
                    return "无法读取PDF文档内容"
            else:
                # 其他文件类型，尝试作为文本读取
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read(1000)  # 只读取前1000个字符
                    if len(content) == 1000:
                        content += "\n\n... (文件内容过长，仅显示前1000个字符)"
                    return content

        except Exception as e:
            return f"无法读取文件内容: {str(e)}"

    def open_settings(self):
        """打开设置窗口"""
        SettingsWindow(self.root, self.config_manager, self.toast, main_app=self)

    def open_rule_manager(self):
        """打开规则管理窗口"""
        try:
            RuleManagerWindow(self.root, self.config_manager, self.toast)
            # 刷新规则显示
            self.refresh_rules_display()
        except Exception as e:
            self.logger.error(f"打开规则管理窗口失败: {e}")
            self.toast.show(f"打开规则管理失败: {str(e)}", "error")

    def add_new_rule(self):
        """添加新规则"""
        try:
            RuleEditorWindow(self.root, self.config_manager, self.toast, mode="add")
            # 刷新规则显示
            self.refresh_rules_display()
        except Exception as e:
            self.logger.error(f"添加新规则失败: {e}")
            self.toast.show(f"添加新规则失败: {str(e)}", "error")

    def add_classification_rule(self):
        """添加分类规则"""
        RuleEditorWindow(self.root, self.config_manager, self.toast, mode="add")

    def edit_classification_rule(self):
        """编辑分类规则"""
        RuleEditorWindow(self.root, self.config_manager, self.toast, mode="edit")

    def delete_classification_rule(self):
        """删除分类规则"""
        RuleManagerWindow(self.root, self.config_manager, self.toast)

    def get_category(self, filename):
        """根据文件名获取分类（从file_sorter_gui移植）"""
        categories = self.config_manager.get_categories()
        matched_categories = []
        default_category = None

        for config in categories:
            # 检查是否匹配当前分类规则
            if config['match_type'] == 'contains' and any(keyword in filename for keyword in config['keywords'] if keyword):
                matched_categories.append(config)
            elif config['match_type'] == 'startswith' and any(filename.startswith(keyword) for keyword in config['keywords'] if keyword):
                matched_categories.append(config)
            elif config['match_type'] == 'default':
                default_category = config['name']

        if matched_categories:
            # 按优先级排序，取最高优先级的分类
            matched_categories.sort(key=lambda x: x.get('priority', 0), reverse=True)
            return matched_categories[0]['name']

        return default_category or "未分类"

    def extract_file_content(self, file_path):
        """提取文件内容（从file_sorter_gui移植）"""
        file_ext = os.path.splitext(file_path)[1].lower()
        content = ""

        try:
            if file_ext in ['.txt', '.md', '.py', '.js', '.html', '.css', '.xml', '.json']:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
            elif file_ext == '.docx':
                doc = docx.Document(file_path)
                content = '\n'.join([para.text for para in doc.paragraphs])
            elif file_ext == '.doc':
                # 需要安装antiword
                content = textract.process(str(file_path)).decode('utf-8', errors='ignore')
            elif file_ext == '.pdf':
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    content = '\n'.join([page.extract_text() for page in reader.pages])
        except Exception as e:
            self.logger.error(f"提取文件 {os.path.basename(file_path)} 内容失败: {e}")
            content = f"[提取内容失败: {str(e)}]"

        # 添加文本标准化处理
        if content:
            # 去除多余空白和标准化换行符
            content = ' '.join(content.split())

        # 使用文件名作为标题
        title = os.path.splitext(os.path.basename(file_path))[0]
        return title, content

    def refresh_theme(self):
        """刷新主题 - 重新应用所有样式"""
        try:
            # 保存当前状态
            current_dir = self.current_directory

            # 重新设置主题
            self.setup_theme()

            # 销毁现有布局（除了Toast）
            widgets_to_destroy = []
            for widget in self.root.winfo_children():
                # 检查是否是Toast相关的窗口
                if hasattr(self, 'toast') and hasattr(self.toast, 'toast_window'):
                    if widget != self.toast.toast_window:
                        widgets_to_destroy.append(widget)
                else:
                    widgets_to_destroy.append(widget)

            for widget in widgets_to_destroy:
                try:
                    widget.destroy()
                except:
                    pass

            # 重新初始化组件（除了已存在的）
            if not hasattr(self, 'toast') or self.toast is None:
                self.toast = ToastNotification(self.root)

            # 重新创建布局
            self.create_layout()

            # 恢复状态
            if current_dir:
                self.current_directory = current_dir
                self.load_directory(current_dir)

            # 延迟显示成功消息，避免在重建过程中出错
            self.root.after(100, lambda: self.toast.show("主题已更新", "success"))

        except Exception as e:
            self.logger.error(f"刷新主题失败: {e}")
            if hasattr(self, 'toast') and self.toast:
                self.toast.show(f"刷新主题失败: {str(e)}", "error")

    def refresh_all_widgets(self):
        """递归刷新所有组件的样式"""
        def update_widget(widget):
            try:
                # 更新背景色
                if hasattr(widget, 'configure'):
                    widget_class = widget.winfo_class()

                    if widget_class == 'Frame':
                        # 根据widget的用途设置不同背景
                        if hasattr(widget, '_bg_type'):
                            bg_type = widget._bg_type
                            widget.configure(bg=ModernTheme.COLORS.get(bg_type, ModernTheme.COLORS['list_bg']))
                        else:
                            widget.configure(bg=ModernTheme.COLORS['list_bg'])

                    elif widget_class == 'Label':
                        widget.configure(
                            bg=ModernTheme.COLORS['list_bg'],
                            fg=ModernTheme.COLORS['text_primary']
                        )

                    elif widget_class == 'Button':
                        # 更新按钮样式
                        if hasattr(widget, '_button_type'):
                            button_type = widget._button_type
                            if button_type == 'primary':
                                widget.configure(bg=ModernTheme.COLORS['primary_start'])
                            elif button_type == 'secondary':
                                widget.configure(bg=ModernTheme.COLORS['nav_bg'])

                # 递归处理子组件
                for child in widget.winfo_children():
                    update_widget(child)

            except Exception as e:
                # 忽略无法更新的组件
                pass

        # 从根窗口开始更新
        update_widget(self.root)

    def generate_summary(self):
        """生成汇总文档（从file_sorter_gui移植）"""
        if not self.category_files or not any(self.category_files.values()):
            self.toast.show("没有可汇总的文件内容，请先进行分类处理", "error")
            return

        self.status_var.set("正在生成汇总文档...")
        self.root.update_idletasks()

        try:
            # 初始化汇总文档
            summary_doc = self.init_summary_doc()

            # 添加目录
            toc_para = self.add_table_of_contents(summary_doc)
            summary_doc.add_page_break()

            # 按分类优先级排序并添加内容
            categories = self.config_manager.get_categories()
            category_priority = {config['name']: config.get('priority', 0) for config in categories}
            for category in sorted(self.category_files.keys(), key=lambda x: category_priority.get(x, 0), reverse=True):
                self.add_title_to_summary(summary_doc, category, level=1)
                for title, content in self.category_files[category]:
                    self.add_title_to_summary(summary_doc, title, level=2)
                    self.add_content_to_summary(summary_doc, content)

            # 将目录移动到文档开头
            summary_doc._body._element.insert(0, toc_para._element)

            # 移除原位置的空段落
            for para in summary_doc.paragraphs:
                if not para.text.strip():
                    p = para._element
                    p.getparent().remove(p)
                    break

            # 保存汇总文档 - 添加时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_filename = f"文章汇总_{timestamp}.docx"
            summary_path = os.path.join(self.current_directory, summary_filename)
            summary_doc.save(summary_path)

            self.status_var.set(f"汇总文档已生成: {summary_path}")
            self.logger.info(f"汇总文档已生成: {summary_path}")
            self.toast.show(f"汇总文档已生成: {summary_filename}", "success")

            # 询问是否打开文档
            if messagebox.askyesno("打开文档", "是否立即打开汇总文档?"):
                os.startfile(summary_path)

        except Exception as e:
            self.status_var.set("生成汇总文档失败")
            self.logger.error(f"生成汇总文档失败: {e}")
            self.toast.show(f"生成汇总文档失败: {str(e)}", "error")

    def init_summary_doc(self):
        """初始化汇总文档（从file_sorter_gui移植）"""
        doc = docx.Document()
        # 设置页边距
        sections = doc.sections
        for section in sections:
            section.top_margin = Cm(3.7)
            section.bottom_margin = Cm(3.5)
            section.left_margin = Cm(2.8)
            section.right_margin = Cm(2.6)

            # 添加居中页码
            footer = section.footer
            para = footer.paragraphs[0]
            para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = para.add_run()
            fld_char = OxmlElement('w:fldChar')
            fld_char.set(qn('w:fldCharType'), 'begin')
            run._r.append(fld_char)

            instr_text = OxmlElement('w:instrText')
            instr_text.text = "PAGE"
            run._r.append(instr_text)

            fld_char = OxmlElement('w:fldChar')
            fld_char.set(qn('w:fldCharType'), 'end')
            run._r.append(fld_char)
        return doc

    def add_title_to_summary(self, doc, title, level):
        """添加指定级别的标题到汇总文档（从file_sorter_gui移植）"""
        title_para = doc.add_paragraph(title, style=f'Heading {level}')
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        title_run = title_para.runs[0]
        title_run.font.name = '方正小标宋简体'
        title_run._element.rPr.rFonts.set(qn('w:eastAsia'), '方正小标宋简体')
        title_run.font.color.rgb = RGBColor(0, 0, 0)  # 设置字体为黑色

        if level == 1:
            title_run.font.size = Pt(22)  # 一级标题字号
        else:
            title_run.font.size = Pt(16)  # 二级标题字号

        title_run.font.bold = True
        doc.add_paragraph()  # 添加空行

    def add_content_to_summary(self, doc, content):
        """添加正文内容到汇总文档（从file_sorter_gui移植）"""
        content_para = doc.add_paragraph()
        content_run = content_para.add_run(content)
        content_run.font.name = '仿宋GB2312'
        content_run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋GB2312')
        content_run.font.size = Pt(16)  # 三号字对应16pt
        content_run.font.color.rgb = RGBColor(0, 0, 0)  # 设置字体为黑色
        # 设置首行缩进2个字符
        content_para.paragraph_format.first_line_indent = Pt(32)
        # 设置行距28.95磅
        content_para.paragraph_format.line_spacing = Pt(28.95)
        doc.add_page_break()  # 分页

    def add_table_of_contents(self, doc):
        """添加目录并返回目录段落（从file_sorter_gui移植）"""
        para = doc.add_paragraph()
        run = para.add_run()

        # 添加目录字段代码
        fld_char_begin = OxmlElement('w:fldChar')
        fld_char_begin.set(qn('w:fldCharType'), 'begin')
        run._r.append(fld_char_begin)

        instr_text = OxmlElement('w:instrText')
        instr_text.text = r'TOC \o "1-2" \h \z \u'  # 显示1-2级标题，带超链接
        run._r.append(instr_text)

        fld_char_end = OxmlElement('w:fldChar')
        fld_char_end.set(qn('w:fldCharType'), 'end')
        run._r.append(fld_char_end)

        return para  # 返回目录段落

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_processing:
            if messagebox.askokcancel("确认", "正在处理文件，确定要退出吗？"):
                self.root.destroy()
        else:
            self.root.destroy()


class SettingsWindow:
    """设置窗口"""

    def __init__(self, parent, config_manager, toast, main_app=None):
        self.parent = parent
        self.config_manager = config_manager
        self.toast = toast
        self._main_app_ref = main_app  # 保存主应用引用

        # 创建设置窗口
        self.window = tk.Toplevel(parent)
        self.window.title("系统设置")
        self.window.geometry("600x500")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()

        # 居中显示
        self.center_window()

        # 创建界面
        self.create_interface()

        # 加载当前设置
        self.load_settings()

    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()

        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        window_width = 600
        window_height = 500

        x = parent_x + (parent_width - window_width) // 2
        y = parent_y + (parent_height - window_height) // 2

        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def create_interface(self):
        """创建设置界面"""
        # 主容器
        main_frame = tk.Frame(self.window, bg=ModernTheme.COLORS['list_bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="系统设置",
            font=ModernTheme.FONTS['title'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        title_label.pack(pady=(0, 20))

        # 创建选项卡
        self.create_tabs(main_frame)

        # 按钮区域
        self.create_buttons(main_frame)

    def create_tabs(self, parent):
        """创建选项卡"""
        # 选项卡容器
        tab_frame = tk.Frame(parent, bg=ModernTheme.COLORS['list_bg'])
        tab_frame.pack(fill=tk.BOTH, expand=True)

        # 选项卡按钮
        tab_buttons_frame = tk.Frame(tab_frame, bg=ModernTheme.COLORS['nav_bg'])
        tab_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        self.current_tab = tk.StringVar(value="general")

        # 通用设置选项卡
        general_btn = tk.Button(
            tab_buttons_frame,
            text="通用设置",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['primary_start'],
            relief='flat',
            padx=20,
            pady=8,
            command=lambda: self.switch_tab("general")
        )
        general_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 界面设置选项卡
        ui_btn = tk.Button(
            tab_buttons_frame,
            text="界面设置",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=20,
            pady=8,
            command=lambda: self.switch_tab("ui")
        )
        ui_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 高级设置选项卡
        advanced_btn = tk.Button(
            tab_buttons_frame,
            text="高级设置",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=20,
            pady=8,
            command=lambda: self.switch_tab("advanced")
        )
        advanced_btn.pack(side=tk.LEFT)

        # 保存按钮引用
        self.tab_buttons = {
            "general": general_btn,
            "ui": ui_btn,
            "advanced": advanced_btn
        }

        # 内容区域
        self.content_frame = tk.Frame(tab_frame, bg=ModernTheme.COLORS['list_bg'])
        self.content_frame.pack(fill=tk.BOTH, expand=True)

        # 创建各个选项卡内容
        self.create_general_tab()
        self.create_ui_tab()
        self.create_advanced_tab()

        # 显示默认选项卡
        self.switch_tab("general")

    def create_general_tab(self):
        """创建通用设置选项卡"""
        self.general_frame = tk.Frame(self.content_frame, bg=ModernTheme.COLORS['list_bg'])

        # 目标路径设置
        path_frame = tk.LabelFrame(
            self.general_frame,
            text="文件处理设置",
            font=ModernTheme.FONTS['subtitle'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        path_frame.pack(fill=tk.X, pady=(0, 15))

        # 目标路径
        tk.Label(
            path_frame,
            text="分类目标路径:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))

        path_input_frame = tk.Frame(path_frame, bg=ModernTheme.COLORS['list_bg'])
        path_input_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        self.target_path_var = tk.StringVar()
        self.target_path_entry = tk.Entry(
            path_input_frame,
            textvariable=self.target_path_var,
            font=ModernTheme.FONTS['content'],
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_primary'],
            relief='flat',
            bd=5
        )
        self.target_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        browse_btn = tk.Button(
            path_input_frame,
            text="浏览",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=15,
            command=self.browse_target_path
        )
        browse_btn.pack(side=tk.RIGHT)

        # 自动刷新设置
        auto_frame = tk.LabelFrame(
            self.general_frame,
            text="自动化设置",
            font=ModernTheme.FONTS['subtitle'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        auto_frame.pack(fill=tk.X, pady=(0, 15))

        self.auto_refresh_var = tk.BooleanVar()
        auto_refresh_cb = tk.Checkbutton(
            auto_frame,
            text="自动刷新文件列表",
            variable=self.auto_refresh_var,
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg'],
            selectcolor=ModernTheme.COLORS['nav_bg'],
            relief='flat'
        )
        auto_refresh_cb.pack(anchor=tk.W, padx=10, pady=10)

        self.show_notifications_var = tk.BooleanVar()
        notifications_cb = tk.Checkbutton(
            auto_frame,
            text="显示操作通知",
            variable=self.show_notifications_var,
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg'],
            selectcolor=ModernTheme.COLORS['nav_bg'],
            relief='flat'
        )
        notifications_cb.pack(anchor=tk.W, padx=10, pady=(0, 10))

    def create_ui_tab(self):
        """创建界面设置选项卡"""
        self.ui_frame = tk.Frame(self.content_frame, bg=ModernTheme.COLORS['list_bg'])

        # 主题设置
        theme_frame = tk.LabelFrame(
            self.ui_frame,
            text="主题设置",
            font=ModernTheme.FONTS['subtitle'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        theme_frame.pack(fill=tk.X, pady=(0, 15))

        # 字体大小
        tk.Label(
            theme_frame,
            text="字体大小:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))

        self.font_size_var = tk.StringVar(value="中等")
        font_size_frame = tk.Frame(theme_frame, bg=ModernTheme.COLORS['list_bg'])
        font_size_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        for size in ["小", "中等", "大", "特大"]:
            rb = tk.Radiobutton(
                font_size_frame,
                text=size,
                variable=self.font_size_var,
                value=size,
                font=ModernTheme.FONTS['content'],
                fg=ModernTheme.COLORS['text_primary'],
                bg=ModernTheme.COLORS['list_bg'],
                selectcolor=ModernTheme.COLORS['nav_bg'],
                relief='flat'
            )
            rb.pack(side=tk.LEFT, padx=(0, 15))

        # 颜色方案
        tk.Label(
            theme_frame,
            text="颜色方案:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))

        self.color_scheme_var = tk.StringVar(value="默认蓝色")
        color_frame = tk.Frame(theme_frame, bg=ModernTheme.COLORS['list_bg'])
        color_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        for scheme in ["默认蓝色", "商务灰色", "护眼绿色", "温暖橙色"]:
            rb = tk.Radiobutton(
                color_frame,
                text=scheme,
                variable=self.color_scheme_var,
                value=scheme,
                font=ModernTheme.FONTS['content'],
                fg=ModernTheme.COLORS['text_primary'],
                bg=ModernTheme.COLORS['list_bg'],
                selectcolor=ModernTheme.COLORS['nav_bg'],
                relief='flat'
            )
            rb.pack(anchor=tk.W, padx=(0, 15))

    def create_advanced_tab(self):
        """创建高级设置选项卡"""
        self.advanced_frame = tk.Frame(self.content_frame, bg=ModernTheme.COLORS['list_bg'])

        # 性能设置
        perf_frame = tk.LabelFrame(
            self.advanced_frame,
            text="性能设置",
            font=ModernTheme.FONTS['subtitle'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        perf_frame.pack(fill=tk.X, pady=(0, 15))

        # 批处理大小
        tk.Label(
            perf_frame,
            text="批处理文件数量:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))

        self.batch_size_var = tk.StringVar(value="100")
        batch_entry = tk.Entry(
            perf_frame,
            textvariable=self.batch_size_var,
            font=ModernTheme.FONTS['content'],
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_primary'],
            relief='flat',
            bd=5,
            width=10
        )
        batch_entry.pack(anchor=tk.W, padx=10, pady=(0, 10))

        # 日志设置
        log_frame = tk.LabelFrame(
            self.advanced_frame,
            text="日志设置",
            font=ModernTheme.FONTS['subtitle'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        log_frame.pack(fill=tk.X, pady=(0, 15))

        self.enable_logging_var = tk.BooleanVar(value=True)
        logging_cb = tk.Checkbutton(
            log_frame,
            text="启用详细日志记录",
            variable=self.enable_logging_var,
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg'],
            selectcolor=ModernTheme.COLORS['nav_bg'],
            relief='flat'
        )
        logging_cb.pack(anchor=tk.W, padx=10, pady=10)

    def create_buttons(self, parent):
        """创建按钮区域"""
        button_frame = tk.Frame(parent, bg=ModernTheme.COLORS['list_bg'])
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # 取消按钮
        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=20,
            pady=8,
            command=self.cancel
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 应用按钮
        apply_btn = tk.Button(
            button_frame,
            text="应用",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_white'],
            bg=ModernTheme.COLORS['primary_start'],
            relief='flat',
            padx=20,
            pady=8,
            command=self.apply_settings
        )
        apply_btn.pack(side=tk.RIGHT)

        # 确定按钮
        ok_btn = tk.Button(
            button_frame,
            text="确定",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_white'],
            bg=ModernTheme.COLORS['primary_start'],
            relief='flat',
            padx=20,
            pady=8,
            command=self.ok
        )
        ok_btn.pack(side=tk.RIGHT, padx=(0, 10))

    def switch_tab(self, tab_name):
        """切换选项卡"""
        # 隐藏所有选项卡
        for frame in [self.general_frame, self.ui_frame, self.advanced_frame]:
            frame.pack_forget()

        # 重置所有按钮样式
        for btn in self.tab_buttons.values():
            btn.configure(bg=ModernTheme.COLORS['nav_bg'])

        # 显示选中的选项卡
        if tab_name == "general":
            self.general_frame.pack(fill=tk.BOTH, expand=True)
            self.tab_buttons["general"].configure(bg=ModernTheme.COLORS['primary_start'])
        elif tab_name == "ui":
            self.ui_frame.pack(fill=tk.BOTH, expand=True)
            self.tab_buttons["ui"].configure(bg=ModernTheme.COLORS['primary_start'])
        elif tab_name == "advanced":
            self.advanced_frame.pack(fill=tk.BOTH, expand=True)
            self.tab_buttons["advanced"].configure(bg=ModernTheme.COLORS['primary_start'])

        self.current_tab.set(tab_name)

    def load_settings(self):
        """加载当前设置"""
        try:
            config = self.config_manager.get_config()

            # 加载通用设置
            self.target_path_var.set(config.get('target_path', ''))
            self.auto_refresh_var.set(config.get('auto_refresh', True))
            self.show_notifications_var.set(config.get('show_notifications', True))

            # 加载界面设置
            self.font_size_var.set(config.get('font_size', '中等'))
            self.color_scheme_var.set(config.get('color_scheme', '默认蓝色'))

            # 加载高级设置
            self.batch_size_var.set(str(config.get('batch_size', 100)))
            self.enable_logging_var.set(config.get('enable_logging', True))

        except Exception as e:
            self.toast.show(f"加载设置失败: {str(e)}", "error")

    def browse_target_path(self):
        """浏览目标路径"""
        from tkinter import filedialog
        path = filedialog.askdirectory(title="选择分类目标路径")
        if path:
            self.target_path_var.set(path)

    def apply_settings(self):
        """应用设置"""
        try:
            # 获取新的设置值
            new_font_size = self.font_size_var.get()
            new_color_scheme = self.color_scheme_var.get()

            # 保存配置
            config = {
                'target_path': self.target_path_var.get(),
                'auto_refresh': self.auto_refresh_var.get(),
                'show_notifications': self.show_notifications_var.get(),
                'font_size': new_font_size,
                'color_scheme': new_color_scheme,
                'batch_size': int(self.batch_size_var.get()),
                'enable_logging': self.enable_logging_var.get()
            }

            self.config_manager.update_config(config)

            # 应用主题变化
            current_theme = ModernTheme.get_current_theme()
            current_font = ModernTheme.get_current_font_size()

            theme_changed = current_theme != new_color_scheme
            font_changed = current_font != new_font_size

            if theme_changed:
                ModernTheme.apply_theme(new_color_scheme)

            if font_changed:
                ModernTheme.apply_font_size(new_font_size)

            # 如果有变化，刷新主界面
            if theme_changed or font_changed:
                if self._main_app_ref and hasattr(self._main_app_ref, 'refresh_theme'):
                    self._main_app_ref.refresh_theme()
                else:
                    self.toast.show("设置已保存，请重启应用以应用主题变化", "warning")

            self.toast.show("设置已保存", "success")

        except Exception as e:
            self.toast.show(f"保存设置失败: {str(e)}", "error")

    def ok(self):
        """确定按钮"""
        self.apply_settings()
        self.window.destroy()

    def cancel(self):
        """取消按钮"""
        self.window.destroy()


class RuleEditorWindow:
    """规则编辑器窗口"""

    def __init__(self, parent, config_manager, toast, mode="add", rule_data=None):
        self.parent = parent
        self.config_manager = config_manager
        self.toast = toast
        self.mode = mode  # "add" 或 "edit"
        self.rule_data = rule_data

        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("添加分类规则" if mode == "add" else "编辑分类规则")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()

        # 居中显示
        self.center_window()

        # 创建界面
        self.create_interface()

        # 如果是编辑模式，加载规则数据
        if mode == "edit" and rule_data:
            self.load_rule_data()

    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()

        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        window_width = 500
        window_height = 400

        x = parent_x + (parent_width - window_width) // 2
        y = parent_y + (parent_height - window_height) // 2

        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = tk.Frame(self.window, bg=ModernTheme.COLORS['list_bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_text = "添加分类规则" if self.mode == "add" else "编辑分类规则"
        title_label = tk.Label(
            main_frame,
            text=title_text,
            font=ModernTheme.FONTS['title'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        title_label.pack(pady=(0, 20))

        # 规则名称
        tk.Label(
            main_frame,
            text="规则名称:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).pack(anchor=tk.W, pady=(0, 5))

        self.rule_name_var = tk.StringVar()
        rule_name_entry = tk.Entry(
            main_frame,
            textvariable=self.rule_name_var,
            font=ModernTheme.FONTS['content'],
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_primary'],
            relief='flat',
            bd=5
        )
        rule_name_entry.pack(fill=tk.X, pady=(0, 15))

        # 匹配条件
        condition_frame = tk.LabelFrame(
            main_frame,
            text="匹配条件",
            font=ModernTheme.FONTS['subtitle'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        condition_frame.pack(fill=tk.X, pady=(0, 15))

        # 条件类型
        tk.Label(
            condition_frame,
            text="条件类型:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))

        self.condition_type_var = tk.StringVar(value="文件扩展名")
        condition_types = ["文件扩展名", "文件名包含", "文件大小", "修改时间"]

        for condition_type in condition_types:
            rb = tk.Radiobutton(
                condition_frame,
                text=condition_type,
                variable=self.condition_type_var,
                value=condition_type,
                font=ModernTheme.FONTS['content'],
                fg=ModernTheme.COLORS['text_primary'],
                bg=ModernTheme.COLORS['list_bg'],
                selectcolor=ModernTheme.COLORS['nav_bg'],
                relief='flat'
            )
            rb.pack(anchor=tk.W, padx=20, pady=2)

        # 条件值
        tk.Label(
            condition_frame,
            text="条件值:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))

        self.condition_value_var = tk.StringVar()
        condition_value_entry = tk.Entry(
            condition_frame,
            textvariable=self.condition_value_var,
            font=ModernTheme.FONTS['content'],
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_primary'],
            relief='flat',
            bd=5
        )
        condition_value_entry.pack(fill=tk.X, padx=10, pady=(0, 10))

        # 目标分类
        tk.Label(
            main_frame,
            text="目标分类:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).pack(anchor=tk.W, pady=(0, 5))

        self.target_category_var = tk.StringVar()
        target_category_entry = tk.Entry(
            main_frame,
            textvariable=self.target_category_var,
            font=ModernTheme.FONTS['content'],
            bg=ModernTheme.COLORS['nav_bg'],
            fg=ModernTheme.COLORS['text_primary'],
            relief='flat',
            bd=5
        )
        target_category_entry.pack(fill=tk.X, pady=(0, 20))

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=ModernTheme.COLORS['list_bg'])
        button_frame.pack(fill=tk.X)

        # 取消按钮
        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=20,
            pady=8,
            command=self.cancel
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 保存按钮
        save_btn = tk.Button(
            button_frame,
            text="保存",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_white'],
            bg=ModernTheme.COLORS['primary_start'],
            relief='flat',
            padx=20,
            pady=8,
            command=self.save_rule
        )
        save_btn.pack(side=tk.RIGHT)

    def load_rule_data(self):
        """加载规则数据（编辑模式）"""
        if self.rule_data:
            self.rule_name_var.set(self.rule_data.get('name', ''))
            self.condition_type_var.set(self.rule_data.get('condition_type', '文件扩展名'))
            self.condition_value_var.set(self.rule_data.get('condition_value', ''))
            self.target_category_var.set(self.rule_data.get('target_category', ''))

    def save_rule(self):
        """保存规则"""
        try:
            # 验证输入
            rule_name = self.rule_name_var.get().strip()
            condition_type = self.condition_type_var.get()
            condition_value = self.condition_value_var.get().strip()
            target_category = self.target_category_var.get().strip()

            if not all([rule_name, condition_value, target_category]):
                self.toast.show("请填写所有必填字段", "warning")
                return

            # 创建规则数据
            rule_data = {
                'name': rule_name,
                'condition_type': condition_type,
                'condition_value': condition_value,
                'target_category': target_category,
                'enabled': True
            }

            # 保存到配置
            config = self.config_manager.get_config()
            if 'classification_rules' not in config:
                config['classification_rules'] = []

            if self.mode == "add":
                config['classification_rules'].append(rule_data)
                message = "规则添加成功"
            else:
                # 编辑模式：替换现有规则
                # 这里需要根据实际需求实现规则更新逻辑
                config['classification_rules'].append(rule_data)
                message = "规则更新成功"

            self.config_manager.update_config(config)
            self.toast.show(message, "success")
            self.window.destroy()

        except Exception as e:
            self.toast.show(f"保存规则失败: {str(e)}", "error")

    def cancel(self):
        """取消"""
        self.window.destroy()


class CategoryConfigDialog:
    """分类配置对话框"""

    def __init__(self, parent, category_data=None, existing_names=None):
        self.parent = parent
        self.category_data = category_data or {}
        self.existing_names = existing_names or []
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("编辑分类" if category_data else "添加分类")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.center_window()

        self._create_widgets()
        self._load_data()

    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()

        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        window_width = 500
        window_height = 400

        x = parent_x + (parent_width - window_width) // 2
        y = parent_y + (parent_height - window_height) // 2

        self.dialog.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, bg=ModernTheme.COLORS['list_bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_text = "编辑分类" if self.category_data else "添加分类"
        title_label = tk.Label(
            main_frame,
            text=title_text,
            font=ModernTheme.FONTS['title'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 分类名称
        tk.Label(
            main_frame,
            text="分类名称:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).grid(row=1, column=0, sticky='w', pady=5)

        self.name_var = tk.StringVar()
        name_entry = tk.Entry(
            main_frame,
            textvariable=self.name_var,
            font=ModernTheme.FONTS['content'],
            width=30
        )
        name_entry.grid(row=1, column=1, columnspan=2, sticky='ew', pady=5, padx=(10, 0))

        # 匹配类型
        tk.Label(
            main_frame,
            text="匹配类型:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).grid(row=2, column=0, sticky='w', pady=5)

        self.match_type_var = tk.StringVar(value='contains')
        match_frame = tk.Frame(main_frame, bg=ModernTheme.COLORS['list_bg'])
        match_frame.grid(row=2, column=1, columnspan=2, sticky='ew', pady=5, padx=(10, 0))

        tk.Radiobutton(
            match_frame,
            text="包含关键词",
            variable=self.match_type_var,
            value='contains',
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg'],
            selectcolor=ModernTheme.COLORS['nav_bg']
        ).pack(side=tk.LEFT, padx=(0, 10))

        tk.Radiobutton(
            match_frame,
            text="以关键词开头",
            variable=self.match_type_var,
            value='startswith',
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg'],
            selectcolor=ModernTheme.COLORS['nav_bg']
        ).pack(side=tk.LEFT, padx=(0, 10))

        tk.Radiobutton(
            match_frame,
            text="默认分类",
            variable=self.match_type_var,
            value='default',
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg'],
            selectcolor=ModernTheme.COLORS['nav_bg']
        ).pack(side=tk.LEFT)

        # 优先级
        tk.Label(
            main_frame,
            text="优先级:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).grid(row=3, column=0, sticky='w', pady=5)

        self.priority_var = tk.IntVar(value=1)
        priority_spinbox = tk.Spinbox(
            main_frame,
            from_=1,
            to=10,
            textvariable=self.priority_var,
            font=ModernTheme.FONTS['content'],
            width=10
        )
        priority_spinbox.grid(row=3, column=1, sticky='w', pady=5, padx=(10, 0))

        # 关键词
        tk.Label(
            main_frame,
            text="关键词:",
            font=ModernTheme.FONTS['content'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        ).grid(row=4, column=0, sticky='nw', pady=5)

        tk.Label(
            main_frame,
            text="(每行一个关键词)",
            font=ModernTheme.FONTS['auxiliary'],
            fg=ModernTheme.COLORS['text_muted'],
            bg=ModernTheme.COLORS['list_bg']
        ).grid(row=4, column=1, sticky='nw', pady=5, padx=(10, 0))

        # 关键词文本框
        keywords_frame = tk.Frame(main_frame, bg=ModernTheme.COLORS['list_bg'])
        keywords_frame.grid(row=5, column=0, columnspan=3, sticky='nsew', pady=5)

        self.keywords_text = tk.Text(
            keywords_frame,
            height=8,
            font=ModernTheme.FONTS['content'],
            wrap=tk.WORD
        )

        keywords_scroll = tk.Scrollbar(keywords_frame, orient=tk.VERTICAL, command=self.keywords_text.yview)
        self.keywords_text.configure(yscrollcommand=keywords_scroll.set)

        self.keywords_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        keywords_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 按钮
        button_frame = tk.Frame(main_frame, bg=ModernTheme.COLORS['list_bg'])
        button_frame.grid(row=6, column=0, columnspan=3, pady=20)

        ok_btn = tk.Button(
            button_frame,
            text="确定",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_white'],
            bg=ModernTheme.COLORS['primary_start'],
            relief='flat',
            padx=20,
            pady=8,
            command=self._on_ok
        )
        ok_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=20,
            pady=8,
            command=self._on_cancel
        )
        cancel_btn.pack(side=tk.LEFT)

        # 配置网格权重
        main_frame.grid_columnconfigure(1, weight=1)
        main_frame.grid_rowconfigure(5, weight=1)

    def _load_data(self):
        """加载数据"""
        if self.category_data:
            self.name_var.set(self.category_data.get('name', ''))
            self.match_type_var.set(self.category_data.get('match_type', 'contains'))
            self.priority_var.set(self.category_data.get('priority', 1))

            keywords = self.category_data.get('keywords', [])
            self.keywords_text.insert(tk.END, '\n'.join(keywords))

    def _on_ok(self):
        """确定按钮"""
        name = self.name_var.get().strip()
        if not name:
            from tkinter import messagebox
            messagebox.showwarning("警告", "分类名称不能为空")
            return

        # 检查名称冲突
        original_name = self.category_data.get('name', '') if self.category_data else ''
        if name != original_name and name in self.existing_names:
            from tkinter import messagebox
            messagebox.showwarning("警告", "此分类名称已存在")
            return

        # 获取关键词
        keywords_text = self.keywords_text.get('1.0', tk.END)
        keywords = [line.strip() for line in keywords_text.split('\n') if line.strip()]

        self.result = {
            'name': name,
            'keywords': keywords,
            'match_type': self.match_type_var.get(),
            'priority': self.priority_var.get()
        }

        self.dialog.destroy()

    def _on_cancel(self):
        """取消按钮"""
        self.result = None
        self.dialog.destroy()

    def get_result(self):
        """获取结果"""
        return self.result


class RuleManagerWindow:
    """规则管理器窗口"""

    def __init__(self, parent, config_manager, toast):
        self.parent = parent
        self.config_manager = config_manager
        self.toast = toast

        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("分类规则管理")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()

        # 居中显示
        self.center_window()

        # 创建界面
        self.create_interface()

        # 加载规则列表
        self.load_rules()

    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()

        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        window_width = 800
        window_height = 600

        x = parent_x + (parent_width - window_width) // 2
        y = parent_y + (parent_height - window_height) // 2

        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = tk.Frame(self.window, bg=ModernTheme.COLORS['list_bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="分类规则管理",
            font=ModernTheme.FONTS['title'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['list_bg']
        )
        title_label.pack(pady=(0, 20))

        # 工具栏
        toolbar_frame = tk.Frame(main_frame, bg=ModernTheme.COLORS['list_bg'])
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # 添加分类按钮
        add_btn = tk.Button(
            toolbar_frame,
            text="➕ 添加分类",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_white'],
            bg=ModernTheme.COLORS['primary_start'],
            relief='flat',
            padx=15,
            pady=5,
            command=self.add_category
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 编辑分类按钮
        edit_btn = tk.Button(
            toolbar_frame,
            text="✏️ 编辑分类",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=15,
            pady=5,
            command=self.edit_category
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 删除分类按钮
        delete_btn = tk.Button(
            toolbar_frame,
            text="🗑️ 删除分类",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_white'],
            bg=ModernTheme.COLORS['error_bg'],
            relief='flat',
            padx=15,
            pady=5,
            command=self.delete_category
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 分隔符
        separator = tk.Frame(toolbar_frame, width=2, bg=ModernTheme.COLORS['border_light'])
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=10)

        # 优先级调整按钮
        priority_up_btn = tk.Button(
            toolbar_frame,
            text="⬆️ 提高优先级",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=15,
            pady=5,
            command=self.increase_priority
        )
        priority_up_btn.pack(side=tk.LEFT, padx=(0, 10))

        priority_down_btn = tk.Button(
            toolbar_frame,
            text="⬇️ 降低优先级",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=15,
            pady=5,
            command=self.decrease_priority
        )
        priority_down_btn.pack(side=tk.LEFT)

        # 分类列表
        list_frame = tk.Frame(main_frame, bg=ModernTheme.COLORS['list_bg'])
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # 创建Treeview
        columns = ('name', 'priority', 'keywords', 'match_type')
        self.category_tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show='headings',
            style='Modern.Treeview'
        )

        # 配置列 - 所有列标题居中显示
        self.category_tree.heading('name', text='分类名称', anchor=tk.CENTER)
        self.category_tree.heading('priority', text='优先级', anchor=tk.CENTER)
        self.category_tree.heading('keywords', text='关键词数量', anchor=tk.CENTER)
        self.category_tree.heading('match_type', text='匹配类型', anchor=tk.CENTER)

        self.category_tree.column('name', width=200)
        self.category_tree.column('priority', width=80, anchor=tk.CENTER)
        self.category_tree.column('keywords', width=100, anchor=tk.CENTER)
        self.category_tree.column('match_type', width=120, anchor=tk.CENTER)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.category_tree.yview)
        self.category_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.category_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.category_tree.bind('<Double-1>', self.edit_category)

        # 关闭按钮
        close_btn = tk.Button(
            main_frame,
            text="关闭",
            font=ModernTheme.FONTS['button'],
            fg=ModernTheme.COLORS['text_primary'],
            bg=ModernTheme.COLORS['nav_bg'],
            relief='flat',
            padx=20,
            pady=8,
            command=self.close
        )
        close_btn.pack()

    def load_rules(self):
        """加载分类规则列表"""
        # 清空现有项目
        for item in self.category_tree.get_children():
            self.category_tree.delete(item)

        try:
            categories = self.config_manager.get_categories()
            for category in sorted(categories, key=lambda x: x.get('priority', 0), reverse=True):
                keyword_count = len(category.get('keywords', []))
                match_type_text = {
                    'contains': '包含关键词',
                    'startswith': '以关键词开头',
                    'default': '默认分类'
                }.get(category.get('match_type', 'contains'), '包含关键词')

                self.category_tree.insert('', tk.END, values=(
                    category['name'],
                    category.get('priority', 1),
                    keyword_count,
                    match_type_text
                ))

        except Exception as e:
            self.toast.show(f"加载分类规则失败: {str(e)}", "error")

    def add_category(self):
        """添加新分类"""
        try:
            existing_names = [c['name'] for c in self.config_manager.get_categories()]
            dialog = CategoryConfigDialog(self.window, existing_names=existing_names)
            self.window.wait_window(dialog.dialog)

            result = dialog.get_result()
            if result:
                self.config_manager.add_category(
                    result['name'],
                    result['keywords'],
                    result['match_type'],
                    result['priority']
                )
                self.load_rules()
                self.toast.show(f"已添加分类: {result['name']}", "success")

        except Exception as e:
            self.toast.show(f"添加分类失败: {str(e)}", "error")

    def edit_category(self, _event=None):
        """编辑选中的分类"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            self.toast.show("请先选择一个分类", "warning")
            return

        try:
            category_name = self.category_tree.item(selected_item[0], 'values')[0]
            category_data = self.config_manager.get_category_by_name(category_name)

            if not category_data:
                self.toast.show("找不到选中的分类", "error")
                return

            existing_names = [c['name'] for c in self.config_manager.get_categories()]
            dialog = CategoryConfigDialog(self.window, category_data, existing_names)
            self.window.wait_window(dialog.dialog)

            result = dialog.get_result()
            if result:
                self.config_manager.update_category(
                    category_name,
                    result['name'],
                    result['keywords'],
                    result['match_type'],
                    result['priority']
                )
                self.load_rules()
                self.toast.show(f"已更新分类: {result['name']}", "success")

        except Exception as e:
            self.toast.show(f"编辑分类失败: {str(e)}", "error")

    def delete_category(self):
        """删除选中的分类"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            self.toast.show("请先选择一个分类", "warning")
            return

        try:
            category_name = self.category_tree.item(selected_item[0], 'values')[0]

            from tkinter import messagebox
            if messagebox.askyesno("确认删除", f"确定要删除分类 '{category_name}' 吗？"):
                self.config_manager.delete_category(category_name)
                self.load_rules()
                self.toast.show(f"已删除分类: {category_name}", "success")

        except Exception as e:
            self.toast.show(f"删除分类失败: {str(e)}", "error")

    def increase_priority(self):
        """提高选中分类的优先级"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            self.toast.show("请先选择一个分类", "warning")
            return

        try:
            category_name = self.category_tree.item(selected_item[0], 'values')[0]
            self.config_manager.adjust_category_priority(category_name, 1)
            self.load_rules()
            self.toast.show(f"已提高 '{category_name}' 的优先级", "success")

        except Exception as e:
            self.toast.show(f"调整优先级失败: {str(e)}", "error")

    def decrease_priority(self):
        """降低选中分类的优先级"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            self.toast.show("请先选择一个分类", "warning")
            return

        try:
            category_name = self.category_tree.item(selected_item[0], 'values')[0]
            self.config_manager.adjust_category_priority(category_name, -1)
            self.load_rules()
            self.toast.show(f"已降低 '{category_name}' 的优先级", "success")

        except Exception as e:
            self.toast.show(f"调整优先级失败: {str(e)}", "error")

    def close(self):
        """关闭窗口"""
        self.window.destroy()


def main():
    """主函数"""
    root = tk.Tk()
    app = FileSorterApp2(root)
    root.mainloop()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
分类规则管理功能演示
展示从file_sorter_gui_new.py迁移到gui_new2.0.py的完整功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_category_management():
    """演示分类规则管理功能"""
    print("=" * 60)
    print("分类规则管理功能演示")
    print("=" * 60)
    
    try:
        # 导入gui_new2.0.py中的类
        import importlib.util
        spec = importlib.util.spec_from_file_location("gui_new2", "gui_new2.0.py")
        gui_new2 = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gui_new2)
        
        ConfigManager = gui_new2.ConfigManager
        RuleManagerWindow = gui_new2.RuleManagerWindow
        ToastNotification = gui_new2.ToastNotification
        
        # 创建主窗口
        root = tk.Tk()
        root.title("分类规则管理演示")
        root.geometry("400x300")
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建Toast通知
        toast = ToastNotification(root)
        
        # 创建演示界面
        main_frame = tk.Frame(root, bg='#F9FBFC', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="分类规则管理功能演示",
            font=('Microsoft YaHei', 16, 'bold'),
            fg='#2E3A59',
            bg='#F9FBFC'
        )
        title_label.pack(pady=(0, 20))
        
        # 功能说明
        info_text = """
已成功迁移以下功能：

✅ 分类规则列表显示
✅ 添加新分类规则
✅ 编辑现有分类规则
✅ 删除分类规则
✅ 调整分类优先级
✅ 分类配置对话框
✅ Toast通知反馈

点击下方按钮打开分类规则管理器
        """
        
        info_label = tk.Label(
            main_frame,
            text=info_text,
            font=('Microsoft YaHei', 10),
            fg='#5A6C7D',
            bg='#F9FBFC',
            justify=tk.LEFT
        )
        info_label.pack(pady=(0, 20))
        
        # 当前分类统计
        categories = config_manager.get_categories()
        stats_text = f"当前分类数量: {len(categories)} 个"
        
        stats_label = tk.Label(
            main_frame,
            text=stats_text,
            font=('Microsoft YaHei', 12, 'bold'),
            fg='#4CAFFE',
            bg='#F9FBFC'
        )
        stats_label.pack(pady=(0, 20))
        
        # 打开规则管理器按钮
        def open_rule_manager():
            try:
                rule_manager = RuleManagerWindow(root, config_manager, toast)
                # 刷新统计信息
                def refresh_stats():
                    categories = config_manager.get_categories()
                    stats_label.config(text=f"当前分类数量: {len(categories)} 个")
                
                # 定期刷新统计
                root.after(1000, refresh_stats)
                
            except Exception as e:
                toast.show(f"打开规则管理器失败: {str(e)}", "error")
        
        open_btn = tk.Button(
            main_frame,
            text="🔧 打开分类规则管理器",
            font=('Microsoft YaHei', 12, 'bold'),
            fg='white',
            bg='#4CAFFE',
            relief='flat',
            padx=20,
            pady=10,
            command=open_rule_manager
        )
        open_btn.pack(pady=(0, 10))
        
        # 功能列表
        features_text = """
主要功能：
• 添加分类：支持关键词匹配、开头匹配、默认分类
• 编辑分类：修改名称、关键词、匹配类型、优先级
• 删除分类：安全删除确认机制
• 优先级调整：提高/降低分类优先级
• 实时反馈：Toast通知系统
        """
        
        features_label = tk.Label(
            main_frame,
            text=features_text,
            font=('Microsoft YaHei', 9),
            fg='#7A8A9A',
            bg='#F9FBFC',
            justify=tk.LEFT
        )
        features_label.pack()
        
        # 显示现有分类列表
        def show_categories():
            categories = config_manager.get_categories()
            category_names = [c['name'] for c in categories]
            toast.show(f"现有分类: {', '.join(category_names[:5])}{'...' if len(category_names) > 5 else ''}", "info")
        
        show_categories_btn = tk.Button(
            main_frame,
            text="📋 查看现有分类",
            font=('Microsoft YaHei', 10),
            fg='#2E3A59',
            bg='#E8F2FF',
            relief='flat',
            padx=15,
            pady=5,
            command=show_categories
        )
        show_categories_btn.pack(pady=(10, 0))
        
        # 启动演示
        print("✅ 演示界面创建成功")
        print("✅ 配置管理器初始化完成")
        print(f"✅ 当前分类数量: {len(categories)}")
        print("\n🚀 启动演示界面...")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 演示启动失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_category_management()

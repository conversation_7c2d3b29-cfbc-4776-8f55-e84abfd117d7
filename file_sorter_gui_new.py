"""
文件自动分类工具 - 重构后的GUI版本
使用模块化架构，改进的用户界面和错误处理
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import shutil
import threading
import logging
from datetime import datetime
import winsound
import platform

# 导入我们的核心模块
from core.file_processor import FileProcessor
from core.config_manager import ConfigManager
from core.document_generator import DocumentGenerator
from core.exceptions import FileSorterError, ConfigError, ProcessingError
from core.logger import LoggerConfig, get_main_logger, setup_module_logger
from core.constants import (
    APP_TITLE, DEFAULT_WINDOW_WIDTH, DEFAULT_WINDOW_HEIGHT,
    MIN_WINDOW_WIDTH, MIN_WINDOW_HEIGHT, FILE_ICONS, CATEGORY_COLORS,
    BATCH_DISPLAY_SIZE, DEFAULT_SOURCE_DIR_NAME, DEFAULT_TARGET_DIR_NAME
)
from core.ui_theme import theme, icons, notification
from core.layout_manager import ResponsiveLayout, ComponentFactory
from core.interaction_manager import create_interaction_manager
from utils.ui_helpers import UIHelpers, ProgressDialog, FilePreviewDialog, CategoryConfigDialog


class FileSorterApp:
    """文件自动分类工具主应用 - 现代化重新设计版本"""

    def __init__(self, root):
        self.root = root
        self.root.title(APP_TITLE)
        self.root.geometry(f"{DEFAULT_WINDOW_WIDTH}x{DEFAULT_WINDOW_HEIGHT}")
        self.root.minsize(MIN_WINDOW_WIDTH, MIN_WINDOW_HEIGHT)

        # 设置现代化主题
        self.style = theme.configure_ttk_styles(root)

        # 设置日志记录器
        self.logger = setup_module_logger(__name__)

        # 初始化核心组件
        try:
            self.config_manager = ConfigManager()
            self.file_processor = FileProcessor(self.config_manager)
            self.document_generator = DocumentGenerator(self.config_manager)
            self.logger.info("核心组件初始化成功")
        except Exception as e:
            error_msg = f"初始化核心组件失败: {str(e)}"
            self.logger.error(error_msg)
            UIHelpers.show_error("初始化错误", error_msg)
            sys.exit(1)
        
        # 应用状态变量
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.work_dir = self.script_dir  # 待分类路径（默认为脚本目录）
        self.target_dir = os.path.join(self.script_dir, DEFAULT_TARGET_DIR_NAME)  # 已分类路径（默认为脚本目录下的"已分类"文件夹）
        self.file_list = []
        self.processed_files = []
        self.processing = False
        
        # 文件格式选择变量
        self.txt_var = tk.BooleanVar(value=True)
        self.doc_var = tk.BooleanVar(value=True)
        self.pdf_var = tk.BooleanVar(value=True)

        # 路径变量
        self.dir_var = tk.StringVar(value=self.work_dir)
        self.source_dir_var = tk.StringVar(value=self.work_dir)
        self.target_dir_var = tk.StringVar(value=self.target_dir)
        
        # 操作历史（用于撤销功能）
        self.operation_history = []
        
        # 初始化布局管理器
        self.layout = ResponsiveLayout(self.root)

        # 初始化交互管理器
        self.interaction = create_interaction_manager(self.root)

        # 创建现代化界面
        self.create_modern_interface()

        # 绑定事件
        self.setup_event_bindings()

        # 首次运行显示向导
        if not os.path.exists('first_run.flag'):
            self.show_welcome_wizard()
            with open('first_run.flag', 'w', encoding='utf-8') as f:
                f.write('已运行')

        # 初始化界面数据
        self.refresh_directory_tree()
        self.refresh_file_list()
        self.update_category_stats()
    
    def create_modern_interface(self):
        """创建现代化GUI界面"""
        # 创建主菜单
        self.create_menu()

        # 创建主布局
        self.main_container = self.layout.create_main_layout()

        # 创建各个面板的内容
        self.create_toolbar_content()
        self.create_directory_panel()
        self.create_file_list_panel()
        self.create_config_panel()
        self.create_status_content()

        # 调整面板比例
        self.layout.adjust_panel_weights(25, 45, 30)

    def create_toolbar_content(self):
        """创建工具栏内容"""
        toolbar_panels = self.layout.get_panel('toolbar')

        # 左侧操作按钮组
        left_group = toolbar_panels['left_group']

        # 路径选择按钮
        self.btn_select_source = ComponentFactory.create_icon_button(
            left_group, "选择待分类目录", icons.get_action_icon('folder'),
            command=self.browse_source_directory, style='Secondary.TButton',
            tooltip="选择包含待分类文件的目录 (Ctrl+O)"
        )
        self.btn_select_source.pack(side=tk.LEFT, padx=(0, theme.SPACING['sm']))

        self.btn_select_target = ComponentFactory.create_icon_button(
            left_group, "选择已分类目录", icons.get_action_icon('folder'),
            command=self.browse_target_directory, style='Secondary.TButton',
            tooltip="选择分类后文件的存储目录"
        )
        self.btn_select_target.pack(side=tk.LEFT, padx=(0, theme.SPACING['sm']))

        # 分割线
        separator1 = theme.create_separator(left_group, orient='vertical')
        separator1.pack(side=tk.LEFT, fill=tk.Y, padx=theme.SPACING['sm'])

        # 主要操作按钮
        self.btn_start_process = ComponentFactory.create_icon_button(
            left_group, "开始分类", icons.get_action_icon('play'),
            command=self.start_processing, style='Primary.TButton',
            tooltip="开始自动分类处理选中的文件"
        )
        self.btn_start_process.pack(side=tk.LEFT, padx=(0, theme.SPACING['sm']))

        self.btn_refresh = ComponentFactory.create_icon_button(
            left_group, "刷新", icons.get_action_icon('refresh'),
            command=self.refresh_all, style='Secondary.TButton',
            tooltip="刷新目录树和文件列表 (F5)"
        )
        self.btn_refresh.pack(side=tk.LEFT)

        # 中间搜索区域
        search_group = toolbar_panels['search_group']

        search_label = ttk.Label(search_group, text="搜索:", style='Muted.TLabel')
        search_label.pack(side=tk.LEFT, padx=(0, theme.SPACING['sm']))

        self.search_entry = ComponentFactory.create_search_entry(
            search_group, placeholder="输入文件名或关键词..."
        )
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, theme.SPACING['sm']))

        self.btn_search = ComponentFactory.create_icon_button(
            search_group, "", icons.get_action_icon('search'),
            command=self.perform_search, style='Secondary.TButton'
        )
        self.btn_search.pack(side=tk.LEFT)

        # 右侧设置按钮组
        right_group = toolbar_panels['right_group']

        self.btn_settings = ComponentFactory.create_icon_button(
            right_group, "设置", icons.get_action_icon('settings'),
            command=self.show_settings, style='Secondary.TButton'
        )
        self.btn_settings.pack(side=tk.LEFT, padx=(theme.SPACING['sm'], 0))

    def create_directory_panel(self):
        """创建目录浏览面板"""
        left_panel = self.layout.get_panel('left')

        # 添加操作按钮到标题栏
        btn_frame = left_panel['buttons']

        self.btn_expand_all = ComponentFactory.create_icon_button(
            btn_frame, "", icons.get_action_icon('expand'),
            command=self.expand_all_directories, style='Secondary.TButton'
        )
        self.btn_expand_all.pack(side=tk.RIGHT, padx=(theme.SPACING['xs'], 0))

        self.btn_collapse_all = ComponentFactory.create_icon_button(
            btn_frame, "", icons.get_action_icon('collapse'),
            command=self.collapse_all_directories, style='Secondary.TButton'
        )
        self.btn_collapse_all.pack(side=tk.RIGHT, padx=(theme.SPACING['xs'], 0))

        # 路径显示和选择
        path_frame = left_panel['path_frame']

        # 当前路径显示
        path_label = ttk.Label(path_frame, text="当前路径:", style='Muted.TLabel')
        path_label.pack(anchor=tk.W)

        self.current_path_var = tk.StringVar(value=self.work_dir)
        self.path_display = ttk.Label(path_frame, textvariable=self.current_path_var,
                                     style='Muted.TLabel', wraplength=200)
        self.path_display.pack(anchor=tk.W, pady=(0, theme.SPACING['sm']))

        # 创建目录树
        tree_frame = left_panel['tree_frame']

        self.directory_tree, self.dir_v_scroll, self.dir_h_scroll = ComponentFactory.create_modern_treeview(
            tree_frame, columns=('path', 'category'), show='tree headings'
        )

        # 配置目录树列
        self.directory_tree.heading('#0', text='目录结构', anchor=tk.W)
        self.directory_tree.heading('path', text='路径')
        self.directory_tree.heading('category', text='分类')

        self.directory_tree.column('#0', width=200, minwidth=150)
        self.directory_tree.column('path', width=0, minwidth=0, stretch=False)  # 隐藏路径列
        self.directory_tree.column('category', width=80, minwidth=60)

    def create_file_list_panel(self):
        """创建文件列表面板"""
        center_panel = self.layout.get_panel('center')

        # 添加控制按钮
        control_frame = center_panel['controls']

        self.btn_select_all = ComponentFactory.create_icon_button(
            control_frame, "全选", icons.get_action_icon('check'),
            command=self.select_all_files, style='Secondary.TButton'
        )
        self.btn_select_all.pack(side=tk.RIGHT, padx=(theme.SPACING['xs'], 0))

        self.btn_preview = ComponentFactory.create_icon_button(
            control_frame, "预览", icons.get_action_icon('preview'),
            command=self.preview_selected_file, style='Secondary.TButton'
        )
        self.btn_preview.pack(side=tk.RIGHT, padx=(theme.SPACING['xs'], 0))

        # 文件类型过滤器
        filter_frame = center_panel['filter_frame']

        filter_label = ttk.Label(filter_frame, text="文件类型:", style='Muted.TLabel')
        filter_label.pack(side=tk.LEFT, padx=(0, theme.SPACING['sm']))

        # 文件类型复选框（使用已定义的变量）

        txt_check = ttk.Checkbutton(filter_frame, text="📄 TXT", variable=self.txt_var,
                                   command=self.refresh_file_list, style='Modern.TCheckbutton')
        txt_check.pack(side=tk.LEFT, padx=(0, theme.SPACING['sm']))

        doc_check = ttk.Checkbutton(filter_frame, text="📝 DOC/DOCX", variable=self.doc_var,
                                   command=self.refresh_file_list, style='Modern.TCheckbutton')
        doc_check.pack(side=tk.LEFT, padx=(0, theme.SPACING['sm']))

        pdf_check = ttk.Checkbutton(filter_frame, text="📋 PDF", variable=self.pdf_var,
                                   command=self.refresh_file_list, style='Modern.TCheckbutton')
        pdf_check.pack(side=tk.LEFT)

        # 创建文件列表
        list_frame = center_panel['list_frame']

        self.file_tree, self.file_v_scroll, self.file_h_scroll = ComponentFactory.create_modern_treeview(
            list_frame, columns=('size', 'category', 'status'), show='tree headings'
        )

        # 配置文件列表列
        self.file_tree.heading('#0', text='文件名', anchor=tk.W)
        self.file_tree.heading('size', text='大小', anchor=tk.E)
        self.file_tree.heading('category', text='分类', anchor=tk.CENTER)
        self.file_tree.heading('status', text='状态', anchor=tk.CENTER)

        self.file_tree.column('#0', width=300, minwidth=200)
        self.file_tree.column('size', width=80, minwidth=60, anchor=tk.E)
        self.file_tree.column('category', width=100, minwidth=80, anchor=tk.CENTER)
        self.file_tree.column('status', width=80, minwidth=60, anchor=tk.CENTER)

    def create_config_panel(self):
        """创建配置面板"""
        bottom_panel = self.layout.get_panel('bottom')
        notebook = bottom_panel['notebook']

        # 分类配置标签页
        config_frame = bottom_panel['config_frame']

        # 创建分类统计表格
        stats_frame = ttk.Frame(config_frame)
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=theme.SPACING['sm'], pady=theme.SPACING['sm'])

        self.category_tree, self.cat_v_scroll, self.cat_h_scroll = ComponentFactory.create_modern_treeview(
            stats_frame, columns=('count', 'files'), show='tree headings'
        )

        # 配置分类统计列
        self.category_tree.heading('#0', text='分类名称', anchor=tk.W)
        self.category_tree.heading('count', text='关键词数', anchor=tk.CENTER)
        self.category_tree.heading('files', text='文件数量', anchor=tk.CENTER)

        self.category_tree.column('#0', width=150, minwidth=100)
        self.category_tree.column('count', width=80, minwidth=60, anchor=tk.CENTER)
        self.category_tree.column('files', width=80, minwidth=60, anchor=tk.CENTER)

        # 处理进度标签页
        progress_frame = bottom_panel['progress_frame']

        # 进度信息区域
        progress_info_frame = ttk.Frame(progress_frame)
        progress_info_frame.pack(fill=tk.X, padx=theme.SPACING['sm'], pady=theme.SPACING['sm'])

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ComponentFactory.create_progress_bar(
            progress_info_frame, variable=self.progress_var, length=300
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, theme.SPACING['sm']))

        # 进度文本
        self.progress_text = ComponentFactory.create_status_label(
            progress_info_frame, text="等待开始处理..."
        )
        self.progress_text.pack(anchor=tk.W)

        # 处理统计
        stats_info_frame = ttk.Frame(progress_frame)
        stats_info_frame.pack(fill=tk.X, padx=theme.SPACING['sm'])

        self.stats_label = ComponentFactory.create_status_label(
            stats_info_frame, text="总文件: 0 | 已处理: 0 | 成功: 0 | 失败: 0"
        )
        self.stats_label.pack(anchor=tk.W)

        # 日志输出标签页
        log_frame = bottom_panel['log_frame']

        # 日志文本区域
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True, padx=theme.SPACING['sm'], pady=theme.SPACING['sm'])

        self.log_text = scrolledtext.ScrolledText(
            log_text_frame, height=8, font=theme.FONTS['code'],
            bg=theme.COLORS['bg_primary'], fg=theme.COLORS['text_primary'],
            wrap=tk.WORD, state=tk.DISABLED
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_status_content(self):
        """创建状态栏内容"""
        status_panel = self.layout.get_panel('status')

        # 左侧状态信息
        left_status = status_panel['left']

        # 创建状态变量（兼容旧代码）
        self.status_var = tk.StringVar(value="就绪")
        self.progress_var = tk.StringVar(value="0")
        self.file_count_var = tk.StringVar(value="文件: 0")

        self.status_label = ComponentFactory.create_status_label(
            left_status, textvariable=self.status_var
        )
        self.status_label.pack(side=tk.LEFT)

        # 中间进度条（用于快速操作反馈）
        progress_frame = status_panel['progress']

        self.mini_progress = ComponentFactory.create_progress_bar(
            progress_frame, length=200, mode='indeterminate'
        )
        # 默认隐藏

        # 右侧统计信息
        right_status = status_panel['right']

        self.file_count_label = ComponentFactory.create_status_label(
            right_status, textvariable=self.file_count_var
        )
        self.file_count_label.pack(side=tk.RIGHT, padx=(theme.SPACING['sm'], 0))

        self.selected_count_var = tk.StringVar(value="已选: 0")
        self.selected_count_label = ComponentFactory.create_status_label(
            right_status, textvariable=self.selected_count_var
        )
        self.selected_count_label.pack(side=tk.RIGHT, padx=(theme.SPACING['sm'], 0))

    def setup_event_bindings(self):
        """设置事件绑定"""
        # 目录树事件
        self.directory_tree.bind('<Button-1>', self.on_directory_click)
        self.directory_tree.bind('<Double-1>', self.on_directory_double_click)
        self.directory_tree.bind('<<TreeviewOpen>>', self.on_directory_expand)

        # 文件列表事件
        self.file_tree.bind('<Button-1>', self.on_file_select)
        self.file_tree.bind('<Double-1>', self.on_file_double_click)
        self.file_tree.bind('<Button-3>', self.on_file_right_click)

        # 分类统计事件
        self.category_tree.bind('<Double-1>', self.on_category_double_click)

        # 搜索框事件
        self.search_entry.bind('<Return>', lambda e: self.perform_search())
        self.search_entry.bind('<KeyRelease>', self.on_search_change)

        # 快捷键绑定
        self.root.bind('<Control-o>', lambda e: self.browse_source_directory())
        self.root.bind('<F5>', lambda e: self.refresh_all())
        self.root.bind('<Control-a>', lambda e: self.select_all_files())
        self.root.bind('<Delete>', lambda e: self.delete_selected_files())
        self.root.bind('<Control-c>', lambda e: self.copy_selected_files())
        self.root.bind('<Control-x>', lambda e: self.cut_selected_files())
        self.root.bind('<Control-v>', lambda e: self.paste_files())

        # 窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.bind('<Configure>', self.on_window_resize)

        # 设置按钮悬停效果
        self.setup_button_interactions()

    def setup_button_interactions(self):
        """设置按钮交互效果"""
        # 为所有按钮添加悬停效果
        for widget in self.root.winfo_children():
            self._add_interactions_recursive(widget)

    def _add_interactions_recursive(self, widget):
        """递归为所有按钮添加交互效果"""
        try:
            # 为按钮添加悬停效果
            if isinstance(widget, ttk.Button):
                self.interaction.hover_manager.add_button_hover_effect(widget)

            # 为其他控件添加基本悬停效果
            elif isinstance(widget, (ttk.Label, ttk.Frame)):
                if hasattr(widget, 'configure'):
                    self.interaction.hover_manager.add_hover_effect(widget)

            # 递归处理子控件
            for child in widget.winfo_children():
                self._add_interactions_recursive(child)
        except:
            pass

    # 新增的便捷方法
    def browse_source_directory(self):
        """选择待分类目录"""
        directory = filedialog.askdirectory(
            title="选择待分类文件目录",
            initialdir=self.work_dir
        )
        if directory:
            self.work_dir = directory
            self.current_path_var.set(directory)
            self.refresh_directory_tree()
            self.refresh_file_list()
            self.logger.info(f"切换待分类目录到: {directory}")
            # 显示操作反馈
            self.interaction.feedback_manager.show_operation_feedback(
                f"已切换到目录: {os.path.basename(directory)}", 'success'
            )

    def browse_target_directory(self):
        """选择已分类目录"""
        directory = filedialog.askdirectory(
            title="选择已分类文件目录",
            initialdir=self.target_dir
        )
        if directory:
            self.target_dir = directory
            self.logger.info(f"设置已分类目录为: {directory}")

    def refresh_all(self):
        """刷新所有界面数据"""
        self.show_mini_progress()
        try:
            self.refresh_directory_tree()
            self.refresh_file_list()
            self.update_category_stats()
            self.update_status("刷新完成")
        finally:
            self.hide_mini_progress()

    def perform_search(self):
        """执行搜索"""
        search_term = self.search_entry.get().strip()
        if not search_term:
            self.refresh_file_list()
            return

        # 过滤文件列表
        filtered_files = []
        for file_info in self.file_list:
            if (search_term.lower() in file_info['name'].lower() or
                search_term.lower() in file_info.get('category', '').lower()):
                filtered_files.append(file_info)

        self.display_file_list(filtered_files)
        self.update_status(f"搜索到 {len(filtered_files)} 个文件")

    def on_search_change(self, event):
        """搜索框内容变化时的实时搜索"""
        # 延迟搜索，避免频繁更新
        if hasattr(self, '_search_timer'):
            self.root.after_cancel(self._search_timer)
        self._search_timer = self.root.after(300, self.perform_search)

    def select_all_files(self):
        """全选文件"""
        for item in self.file_tree.get_children():
            self.file_tree.selection_add(item)
        self.update_selection_count()

    def preview_selected_file(self):
        """预览选中的文件"""
        selection = self.file_tree.selection()
        if not selection:
            UIHelpers.show_warning("提示", "请先选择要预览的文件")
            return

        item = selection[0]
        file_path = self.file_tree.item(item)['values'][0] if self.file_tree.item(item)['values'] else None

        if file_path and os.path.isfile(file_path):
            try:
                title, content = self.file_processor.extract_file_content(file_path)
                FilePreviewDialog(self.root, file_path, content)
            except Exception as e:
                UIHelpers.show_error("错误", f"预览文件失败: {str(e)}")

    def start_processing(self):
        """开始处理文件"""
        if not self.file_list:
            UIHelpers.show_warning("提示", "没有找到可处理的文件")
            return

        # 显示加载指示器
        loading_widget = self.interaction.feedback_manager.show_loading_indicator(
            self.root, "正在分类文件，请稍候..."
        )

        def process_with_feedback():
            try:
                # 这里调用原有的处理逻辑
                self.process_files()

                # 显示成功反馈
                self.interaction.feedback_manager.show_operation_feedback(
                    "文件分类完成！", 'success'
                )
            except Exception as e:
                # 显示错误反馈
                self.interaction.feedback_manager.show_operation_feedback(
                    f"分类失败: {str(e)}", 'error'
                )
            finally:
                # 隐藏加载指示器
                self.interaction.feedback_manager.hide_loading_indicator(loading_widget)

        # 在后台线程中执行处理
        threading.Thread(target=process_with_feedback, daemon=True).start()

    def show_settings(self):
        """显示设置对话框"""
        CategoryConfigDialog(self.root, self.config_manager)
        self.update_category_stats()

    def update_category_stats(self):
        """更新分类统计信息"""
        try:
            # 清空现有统计
            for item in self.category_tree.get_children():
                self.category_tree.delete(item)

            # 获取分类配置
            categories = self.config_manager.get_categories()

            # 如果categories是列表，转换为字典格式
            if isinstance(categories, list):
                categories_dict = {}
                for category in categories:
                    if isinstance(category, dict) and 'name' in category:
                        categories_dict[category['name']] = category
                categories = categories_dict

            # 统计每个分类的文件数量
            for category_name, category_info in categories.items():
                keywords = category_info.get('keywords', [])
                priority = category_info.get('priority', 0)

                # 计算该分类的文件数量
                file_count = 0
                if hasattr(self, 'file_list'):
                    for file_path in self.file_list:
                        if self.config_manager.get_category_for_filename(os.path.basename(file_path)) == category_name:
                            file_count += 1

                # 添加到统计表格
                self.category_tree.insert('', tk.END, values=(
                    category_name,
                    priority,
                    len(keywords),
                    file_count
                ))
        except Exception as e:
            self.logger.error(f"更新分类统计失败: {str(e)}")

    def expand_all_directories(self):
        """展开所有目录"""
        def expand_item(item):
            self.directory_tree.item(item, open=True)
            for child in self.directory_tree.get_children(item):
                expand_item(child)

        for item in self.directory_tree.get_children():
            expand_item(item)

    def collapse_all_directories(self):
        """折叠所有目录"""
        def collapse_item(item):
            self.directory_tree.item(item, open=False)
            for child in self.directory_tree.get_children(item):
                collapse_item(child)

        for item in self.directory_tree.get_children():
            collapse_item(item)

    def show_mini_progress(self):
        """显示小进度条"""
        self.mini_progress.pack(fill=tk.X, expand=True)
        self.mini_progress.start()

    def hide_mini_progress(self):
        """隐藏小进度条"""
        self.mini_progress.stop()
        self.mini_progress.pack_forget()

    def update_status(self, message):
        """更新状态栏消息"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def update_selection_count(self):
        """更新选中文件数量"""
        selected_count = len(self.file_tree.selection())
        self.selected_count_label.config(text=f"已选: {selected_count}")

    def on_window_resize(self, event):
        """窗口大小改变事件"""
        if event.widget == self.root:
            # 可以在这里添加响应式布局调整逻辑
            pass

    def on_closing(self):
        """窗口关闭事件"""
        if self.processing:
            if messagebox.askyesno("确认", "正在处理文件，确定要退出吗？"):
                self.file_processor.cancel_processing()
                self.root.destroy()
        else:
            self.root.destroy()

    # 事件处理方法
    def on_file_select(self, event):
        """文件选择事件"""
        self.update_selection_count()

    def on_file_double_click(self, event):
        """文件双击事件 - 预览文件"""
        self.preview_selected_file()

    def on_file_right_click(self, event):
        """文件右键菜单"""
        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="预览", command=self.preview_selected_file)
        context_menu.add_command(label="复制", command=self.copy_selected_files)
        context_menu.add_command(label="剪切", command=self.cut_selected_files)
        context_menu.add_separator()
        context_menu.add_command(label="删除", command=self.delete_selected_files)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def on_category_double_click(self, event):
        """分类双击事件 - 编辑分类"""
        selection = self.category_tree.selection()
        if selection:
            self.show_settings()

    def copy_selected_files(self):
        """复制选中的文件"""
        # 实现文件复制逻辑
        UIHelpers.show_info("提示", "复制功能开发中...")

    def cut_selected_files(self):
        """剪切选中的文件"""
        # 实现文件剪切逻辑
        UIHelpers.show_info("提示", "剪切功能开发中...")

    def paste_files(self):
        """粘贴文件"""
        # 实现文件粘贴逻辑
        UIHelpers.show_info("提示", "粘贴功能开发中...")

    def delete_selected_files(self):
        """删除选中的文件"""
        selection = self.file_tree.selection()
        if not selection:
            UIHelpers.show_warning("提示", "请先选择要删除的文件")
            return

        if messagebox.askyesno("确认删除", f"确定要删除选中的 {len(selection)} 个文件吗？"):
            # 实现文件删除逻辑
            UIHelpers.show_info("提示", "删除功能开发中...")

    def display_file_list(self, files):
        """显示文件列表"""
        # 清空现有列表
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # 添加文件
        for file_info in files:
            file_icon = icons.get_file_icon(file_info['name'])
            self.file_tree.insert('', tk.END,
                                text=f"{file_icon} {file_info['name']}",
                                values=(file_info.get('size_str', ''),
                                       file_info.get('category', ''),
                                       file_info.get('status', '未处理')))

        # 更新文件计数
        self.file_count_label.config(text=f"文件: {len(files)}")
        self.selected_count_label.config(text="已选: 0")

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="选择工作目录", command=self.browse_directory, accelerator="Ctrl+O")
        file_menu.add_command(label="刷新文件列表", command=self.refresh_file_list, accelerator="F5")
        file_menu.add_separator()
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit, accelerator="Ctrl+Q")
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="撤销操作", command=self.undo_operation, accelerator="Ctrl+Z")
        edit_menu.add_command(label="全选文件", command=self.select_all_files, accelerator="Ctrl+A")
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="开始分类", command=self.start_processing, accelerator="Ctrl+S")
        tools_menu.add_command(label="生成汇总", command=self.generate_summary, accelerator="Ctrl+G")
        tools_menu.add_command(label="生成统计报告", command=self.generate_statistics)
        tools_menu.add_separator()
        tools_menu.add_command(label="查找重复文件", command=self.find_duplicates)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(self.root, padding="5")
        toolbar_frame.pack(fill=tk.X)
        
        # 左侧工具按钮
        left_frame = ttk.Frame(toolbar_frame)
        left_frame.pack(side=tk.LEFT)

        # 路径选择按钮
        path_frame = ttk.LabelFrame(left_frame, text="路径设置", padding="5", style='Modern.TLabelframe')
        path_frame.pack(side=tk.LEFT, padx=5)

        ttk.Button(path_frame, text="📁 待分类路径", command=self.browse_source_directory,
                  width=14, style='Secondary.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(path_frame, text="📂 已分类路径", command=self.browse_target_directory,
                  width=14, style='Secondary.TButton').pack(side=tk.LEFT, padx=2)

        # 分隔符
        ttk.Separator(left_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 操作按钮
        ttk.Button(left_frame, text="🔄 刷新", command=self.refresh_file_list,
                  style='Secondary.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(left_frame, text="🚀 开始分类", command=self.start_processing,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(left_frame, text="📊 生成汇总", command=self.generate_summary,
                  style='Secondary.TButton').pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(left_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        

        
        # 右侧路径显示
        right_frame = ttk.Frame(toolbar_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        # 路径显示框架
        path_display_frame = ttk.LabelFrame(right_frame, text="📍 当前路径", padding="5",
                                          style='Modern.TLabelframe')
        path_display_frame.pack(fill=tk.X, expand=True, padx=5)

        # 待分类路径
        source_frame = ttk.Frame(path_display_frame)
        source_frame.pack(fill=tk.X, pady=2)
        ttk.Label(source_frame, text="📁 待分类:", width=10, style='Muted.TLabel').pack(side=tk.LEFT)
        self.source_dir_var = tk.StringVar(value=self.work_dir)
        source_entry = ttk.Entry(source_frame, textvariable=self.source_dir_var, state='readonly',
                               style='Modern.TEntry')
        source_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

        # 已分类路径
        target_frame = ttk.Frame(path_display_frame)
        target_frame.pack(fill=tk.X, pady=2)
        ttk.Label(target_frame, text="📂 已分类:", width=10, style='Muted.TLabel').pack(side=tk.LEFT)
        self.target_dir_var = tk.StringVar(value=self.target_dir)
        target_entry = ttk.Entry(target_frame, textvariable=self.target_dir_var, state='readonly',
                               style='Modern.TEntry')
        target_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
    
    def create_main_content(self):
        """创建主内容区域"""
        # 创建水平分割的PanedWindow
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧面板 - 目录树
        directory_panel = self.create_directory_panel(main_paned)
        main_paned.add(directory_panel, weight=1)

        # 中间面板 - 分类规则配置
        category_panel = self.create_category_panel(main_paned)
        main_paned.add(category_panel, weight=1)

        # 右侧面板 - 文件列表
        file_panel = self.create_file_panel(main_paned)
        main_paned.add(file_panel, weight=2)



    def create_category_panel(self, parent):
        """创建分类规则配置面板"""
        panel = ttk.LabelFrame(parent, text="分类规则配置", padding="10")
        
        # 分类规则树状视图
        tree_frame = ttk.Frame(panel)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('name', 'priority', 'keywords')
        self.category_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        self.category_tree.heading('name', text='分类名称')
        self.category_tree.heading('priority', text='优先级')
        self.category_tree.heading('keywords', text='关键词数量')
        
        self.category_tree.column('name', width=120)
        self.category_tree.column('priority', width=60, anchor=tk.CENTER)
        self.category_tree.column('keywords', width=80, anchor=tk.CENTER)
        
        # 添加滚动条
        tree_scroll = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.category_tree.yview)
        self.category_tree.configure(yscrollcommand=tree_scroll.set)
        
        self.category_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.category_tree.bind('<Double-1>', self.edit_category)
        
        # 按钮框架
        btn_frame = ttk.Frame(panel)
        btn_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(btn_frame, text="添加", command=self.add_category).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="编辑", command=self.edit_category).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="删除", command=self.delete_category).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(btn_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(btn_frame, text="↑", command=self.increase_priority).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="↓", command=self.decrease_priority).pack(side=tk.LEFT, padx=2)
        
        # 刷新分类列表
        self.refresh_category_list()
        
        return panel
    
    def create_file_panel(self, parent):
        """创建文件列表面板"""
        panel = ttk.LabelFrame(parent, text="文件列表", padding="10")
        
        # 文件操作工具栏
        file_toolbar = ttk.Frame(panel)
        file_toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(file_toolbar, text="预览", command=self.preview_selected_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(file_toolbar, text="打开", command=self.open_selected_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(file_toolbar, text="手动分类", command=self.manual_classify).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(file_toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 搜索框
        ttk.Label(file_toolbar, text="搜索:").pack(side=tk.LEFT, padx=2)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(file_toolbar, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=2)
        search_entry.bind('<KeyRelease>', self.filter_files)
        
        ttk.Button(file_toolbar, text="清除", command=self.clear_search).pack(side=tk.LEFT, padx=2)
        
        # 文件列表树状视图
        tree_frame = ttk.Frame(panel)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('name', 'size', 'category', 'status')
        self.file_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')
        
        # 设置列标题和宽度
        self.file_tree.heading('name', text='文件名')
        self.file_tree.heading('size', text='大小')
        self.file_tree.heading('category', text='预测分类')
        self.file_tree.heading('status', text='状态')
        
        self.file_tree.column('name', width=250)
        self.file_tree.column('size', width=80, anchor='e')
        self.file_tree.column('category', width=100)
        self.file_tree.column('status', width=80)
        
        # 添加滚动条
        file_scroll_v = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        file_scroll_h = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=file_scroll_v.set, xscrollcommand=file_scroll_h.set)
        
        self.file_tree.grid(row=0, column=0, sticky='nsew')
        file_scroll_v.grid(row=0, column=1, sticky='ns')
        file_scroll_h.grid(row=1, column=0, sticky='ew')
        
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定右键菜单
        self.create_file_context_menu()
        self.file_tree.bind('<Button-3>', self.show_file_context_menu)
        
        return panel
    
    def create_file_context_menu(self):
        """创建文件右键菜单"""
        self.file_context_menu = tk.Menu(self.root, tearoff=0)
        self.file_context_menu.add_command(label="预览文件", command=self.preview_selected_file)
        self.file_context_menu.add_command(label="打开文件", command=self.open_selected_file)
        self.file_context_menu.add_command(label="查看属性", command=self.view_file_properties)
        self.file_context_menu.add_separator()
        self.file_context_menu.add_command(label="手动分类", command=self.manual_classify)
        self.file_context_menu.add_command(label="从列表移除", command=self.remove_from_list)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 进度条
        progress_frame = ttk.Frame(status_frame)
        progress_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          length=200, mode='determinate')
        self.progress_bar.pack(side=tk.LEFT, padx=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT, padx=10)
        
        # 文件统计
        self.file_count_var = tk.StringVar(value="文件: 0")
        count_label = ttk.Label(status_frame, textvariable=self.file_count_var)
        count_label.pack(side=tk.RIGHT, padx=5)
    
    def bind_shortcuts(self):
        """绑定快捷键"""
        self.root.bind('<Control-o>', lambda _: self.browse_directory())
        self.root.bind('<Control-s>', lambda _: self.start_processing())
        self.root.bind('<Control-g>', lambda _: self.generate_summary())
        self.root.bind('<Control-z>', lambda _: self.undo_operation())
        self.root.bind('<Control-a>', lambda _: self.select_all_files())
        self.root.bind('<Control-q>', lambda _: self.root.quit())
        self.root.bind('<F5>', lambda _: self.refresh_file_list())
        self.root.bind('<Escape>', lambda _: self.root.quit())

    def populate_directory_tree(self):
        """填充目录树"""
        try:
            # 清空现有内容
            for item in self.directory_tree.get_children():
                self.directory_tree.delete(item)

            if not self.work_dir or not os.path.exists(self.work_dir):
                return

            # 添加根目录
            root_name = os.path.basename(self.work_dir) or self.work_dir
            root_item = self.directory_tree.insert('', 'end', text=f"📁 {root_name}",
                                                  values=(self.work_dir,), open=True)

            # 递归添加子目录和文件
            self._add_directory_items(root_item, self.work_dir, max_depth=3)

        except Exception as e:
            self.logger.error(f"填充目录树失败: {e}")

    def _add_directory_items(self, parent_item, directory_path, current_depth=0, max_depth=3):
        """递归添加目录项"""
        if current_depth >= max_depth:
            return

        try:
            items = []

            # 获取目录内容
            for item_name in os.listdir(directory_path):
                item_path = os.path.join(directory_path, item_name)

                # 跳过隐藏文件和系统文件
                if item_name.startswith('.') or item_name.startswith('$'):
                    continue

                if os.path.isdir(item_path):
                    items.append(('dir', item_name, item_path))
                elif self._is_supported_file(item_name):
                    items.append(('file', item_name, item_path))

            # 排序：目录在前，文件在后，按名称排序
            items.sort(key=lambda x: (x[0] == 'file', x[1].lower()))

            # 添加到树中
            for item_type, item_name, item_path in items:
                if item_type == 'dir':
                    # 添加目录
                    dir_item = self.directory_tree.insert(parent_item, 'end',
                                                         text=f"📁 {item_name}",
                                                         values=(item_path,))

                    # 递归添加子项
                    self._add_directory_items(dir_item, item_path, current_depth + 1, max_depth)

                else:
                    # 添加文件
                    file_icon = self._get_file_icon(item_name)
                    category = self.config_manager.get_category_for_filename(item_name)
                    category_color = self._get_category_color(category)

                    file_item = self.directory_tree.insert(parent_item, 'end',
                                                          text=f"{file_icon} {item_name}",
                                                          values=(item_path, category),
                                                          tags=(category_color,))

        except PermissionError:
            # 权限不足，跳过
            pass
        except Exception as e:
            self.logger.warning(f"添加目录项失败 {directory_path}: {e}")

    def _is_supported_file(self, filename):
        """检查是否为支持的文件类型"""
        supported_extensions = ['.txt', '.doc', '.docx', '.pdf']
        return any(filename.lower().endswith(ext) for ext in supported_extensions)

    def _get_file_icon(self, filename):
        """根据文件类型获取图标"""
        ext = os.path.splitext(filename)[1].lower()
        return FILE_ICONS.get(ext, FILE_ICONS['default'])

    def _get_category_color(self, category):
        """根据分类获取颜色标签"""
        return CATEGORY_COLORS.get(category, CATEGORY_COLORS['default'])

    def refresh_directory_tree(self):
        """刷新目录树"""
        self.populate_directory_tree()
        self.logger.info("目录树已刷新")

    def expand_all_directories(self):
        """展开所有目录"""
        def expand_item(item):
            self.directory_tree.item(item, open=True)
            for child in self.directory_tree.get_children(item):
                expand_item(child)

        for item in self.directory_tree.get_children():
            expand_item(item)

    def collapse_all_directories(self):
        """折叠所有目录"""
        def collapse_item(item):
            self.directory_tree.item(item, open=False)
            for child in self.directory_tree.get_children(item):
                collapse_item(child)

        for item in self.directory_tree.get_children():
            collapse_item(item)

    def on_directory_click(self, _event):
        """处理目录树单击事件"""
        item = self.directory_tree.selection()[0] if self.directory_tree.selection() else None
        if not item:
            return

        values = self.directory_tree.item(item, 'values')
        if not values:
            return

        path = values[0]

        # 如果是目录，更新工作目录并刷新文件列表
        if os.path.isdir(path):
            self.work_dir = path
            self.dir_var.set(path)
            self.refresh_file_list()
            self.logger.info(f"切换到目录: {path}")

    def on_directory_double_click(self, _event):
        """处理目录树双击事件"""
        item = self.directory_tree.selection()[0] if self.directory_tree.selection() else None
        if not item:
            return

        values = self.directory_tree.item(item, 'values')
        if not values:
            return

        path = values[0]

        # 如果是文件，预览文件内容
        if os.path.isfile(path):
            try:
                _title, content = self.file_processor.extract_file_content(path)
                FilePreviewDialog(self.root, path, content)
            except Exception as e:
                UIHelpers.show_error("错误", f"预览文件失败: {str(e)}", self.root)

    def on_directory_expand(self, _event):
        """处理目录展开事件"""
        item = self.directory_tree.selection()[0] if self.directory_tree.selection() else None
        if not item:
            return

        # 可以在这里添加延迟加载逻辑
        pass

    def show_directory_context_menu(self, event):
        """显示目录树右键菜单"""
        item = self.directory_tree.identify_row(event.y)
        if not item:
            return

        self.directory_tree.selection_set(item)
        values = self.directory_tree.item(item, 'values')
        if not values:
            return

        path = values[0]

        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)

        if os.path.isdir(path):
            # 目录操作
            context_menu.add_command(label="设为待分类目录",
                                   command=lambda: self.set_source_directory(path))
            context_menu.add_command(label="设为已分类目录",
                                   command=lambda: self.set_target_directory(path))
            context_menu.add_separator()
            context_menu.add_command(label="在资源管理器中打开",
                                   command=lambda: self.open_in_explorer(path))
            context_menu.add_separator()
            context_menu.add_command(label="复制路径",
                                   command=lambda: self.copy_path_to_clipboard(path))
            context_menu.add_separator()
            context_menu.add_command(label="删除目录",
                                   command=lambda: self.delete_directory(path))
            context_menu.add_separator()
            context_menu.add_command(label="刷新",
                                   command=self.refresh_directory_tree)
        else:
            # 文件操作
            context_menu.add_command(label="预览文件",
                                   command=lambda: self.preview_file_from_tree(path))
            context_menu.add_command(label="在资源管理器中显示",
                                   command=lambda: self.show_in_explorer(path))
            context_menu.add_separator()
            context_menu.add_command(label="添加到处理列表",
                                   command=lambda: self.add_file_to_list(path))
            context_menu.add_separator()
            context_menu.add_command(label="复制文件",
                                   command=lambda: self.copy_file(path))
            context_menu.add_command(label="剪切文件",
                                   command=lambda: self.cut_file(path))
            context_menu.add_command(label="复制路径",
                                   command=lambda: self.copy_path_to_clipboard(path))
            context_menu.add_separator()
            context_menu.add_command(label="删除文件",
                                   command=lambda: self.delete_file(path))

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def set_work_directory(self, path):
        """设置工作目录（兼容性保留）"""
        self.set_source_directory(path)

    def set_source_directory(self, path):
        """设置待分类目录"""
        self.work_dir = path
        self.source_dir_var.set(path)
        self.refresh_file_list()
        self.populate_directory_tree()
        self.logger.info(f"待分类目录设置为: {path}")

    def set_target_directory(self, path):
        """设置已分类目录"""
        self.target_dir = path
        self.target_dir_var.set(path)
        self.logger.info(f"已分类目录设置为: {path}")

    def open_in_explorer(self, path):
        """在资源管理器中打开目录"""
        try:
            import subprocess
            subprocess.Popen(['explorer', path])
        except Exception as e:
            UIHelpers.show_error("错误", f"打开资源管理器失败: {str(e)}", self.root)

    def show_in_explorer(self, file_path):
        """在资源管理器中显示文件"""
        try:
            import subprocess
            subprocess.Popen(['explorer', '/select,', file_path])
        except Exception as e:
            UIHelpers.show_error("错误", f"显示文件失败: {str(e)}", self.root)

    def preview_file_from_tree(self, file_path):
        """从目录树预览文件"""
        try:
            _title, content = self.file_processor.extract_file_content(file_path)
            FilePreviewDialog(self.root, file_path, content)
        except Exception as e:
            UIHelpers.show_error("错误", f"预览文件失败: {str(e)}", self.root)

    def add_file_to_list(self, file_path):
        """将文件添加到处理列表"""
        try:
            # 检查文件是否已在列表中
            for file_info in self.file_list:
                if file_info['path'] == file_path:
                    UIHelpers.show_info("提示", "文件已在处理列表中", self.root)
                    return

            # 创建文件信息
            file_info = {
                'name': os.path.basename(file_path),
                'path': file_path,
                'size': os.path.getsize(file_path),
                'size_str': self._format_file_size(os.path.getsize(file_path)),
                'category': self.config_manager.get_category_for_filename(os.path.basename(file_path)),
                'status': 'pending'
            }

            self.file_list.append(file_info)
            self.refresh_file_display()
            self.logger.info(f"文件已添加到处理列表: {file_path}")

        except Exception as e:
            UIHelpers.show_error("错误", f"添加文件失败: {str(e)}", self.root)

    def copy_path_to_clipboard(self, path):
        """复制路径到剪贴板"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(path)
            self.root.update()  # 确保剪贴板更新
            self.logger.info(f"路径已复制到剪贴板: {path}")
            UIHelpers.show_info("成功", f"路径已复制到剪贴板:\n{path}", self.root)
        except Exception as e:
            UIHelpers.show_error("错误", f"复制路径失败: {str(e)}", self.root)

    def copy_file(self, file_path):
        """复制文件"""
        try:
            # 使用文件对话框选择目标位置
            target_dir = filedialog.askdirectory(title="选择复制目标目录")
            if not target_dir:
                return

            file_name = os.path.basename(file_path)
            target_path = os.path.join(target_dir, file_name)

            # 处理文件名冲突
            if os.path.exists(target_path):
                base_name, ext = os.path.splitext(file_name)
                counter = 1
                while os.path.exists(target_path):
                    new_name = f"{base_name}_{counter}{ext}"
                    target_path = os.path.join(target_dir, new_name)
                    counter += 1

            shutil.copy2(file_path, target_path)
            self.logger.info(f"文件已复制: {file_path} -> {target_path}")
            UIHelpers.show_info("成功", f"文件已复制到:\n{target_path}", self.root)

        except Exception as e:
            UIHelpers.show_error("错误", f"复制文件失败: {str(e)}", self.root)

    def cut_file(self, file_path):
        """剪切文件"""
        try:
            # 使用文件对话框选择目标位置
            target_dir = filedialog.askdirectory(title="选择移动目标目录")
            if not target_dir:
                return

            file_name = os.path.basename(file_path)
            target_path = os.path.join(target_dir, file_name)

            # 处理文件名冲突
            if os.path.exists(target_path):
                base_name, ext = os.path.splitext(file_name)
                counter = 1
                while os.path.exists(target_path):
                    new_name = f"{base_name}_{counter}{ext}"
                    target_path = os.path.join(target_dir, new_name)
                    counter += 1

            shutil.move(file_path, target_path)
            self.logger.info(f"文件已移动: {file_path} -> {target_path}")
            UIHelpers.show_info("成功", f"文件已移动到:\n{target_path}", self.root)

            # 刷新界面
            self.refresh_file_list()
            self.populate_directory_tree()

        except Exception as e:
            UIHelpers.show_error("错误", f"移动文件失败: {str(e)}", self.root)

    def delete_file(self, file_path):
        """删除文件"""
        try:
            file_name = os.path.basename(file_path)

            # 确认删除
            if not UIHelpers.ask_yes_no(
                "确认删除",
                f"确定要删除文件吗？\n\n{file_name}\n\n此操作不可撤销！",
                self.root
            ):
                return

            # 删除文件
            os.remove(file_path)
            self.logger.info(f"文件已删除: {file_path}")
            UIHelpers.show_info("成功", f"文件已删除:\n{file_name}", self.root)

            # 刷新界面
            self.refresh_file_list()
            self.populate_directory_tree()

        except Exception as e:
            UIHelpers.show_error("错误", f"删除文件失败: {str(e)}", self.root)

    def delete_directory(self, dir_path):
        """删除目录"""
        try:
            dir_name = os.path.basename(dir_path)

            # 检查目录是否为空
            if os.listdir(dir_path):
                # 目录不为空，需要额外确认
                if not UIHelpers.ask_yes_no(
                    "确认删除",
                    f"目录不为空，确定要删除整个目录及其所有内容吗？\n\n{dir_name}\n\n此操作不可撤销！",
                    self.root
                ):
                    return

                # 删除整个目录树
                shutil.rmtree(dir_path)
            else:
                # 目录为空，简单确认
                if not UIHelpers.ask_yes_no(
                    "确认删除",
                    f"确定要删除空目录吗？\n\n{dir_name}",
                    self.root
                ):
                    return

                # 删除空目录
                os.rmdir(dir_path)

            self.logger.info(f"目录已删除: {dir_path}")
            UIHelpers.show_info("成功", f"目录已删除:\n{dir_name}", self.root)

            # 刷新界面
            self.refresh_file_list()
            self.populate_directory_tree()

        except Exception as e:
            UIHelpers.show_error("错误", f"删除目录失败: {str(e)}", self.root)

    def _format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

    def show_welcome_wizard(self):
        """显示欢迎向导"""
        wizard = tk.Toplevel(self.root)
        wizard.title("欢迎使用文件自动分类工具 v2.0")
        wizard.geometry("600x400")
        wizard.resizable(False, False)
        wizard.transient(self.root)
        wizard.grab_set()
        
        UIHelpers.center_window(wizard, 600, 400)
        
        # 创建向导内容
        notebook = ttk.Notebook(wizard)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 页面1: 欢迎
        page1 = ttk.Frame(notebook)
        notebook.add(page1, text="欢迎")
        
        welcome_text = """欢迎使用文件自动分类工具 v2.0！

本工具经过全面重构，具有以下新特性：
• 模块化架构，更稳定可靠
• 改进的用户界面和操作体验
• 增强的错误处理和异常恢复
• 新增文件预览和批量操作功能
• 支持操作撤销和统计分析
• 优化的文件处理性能

让我们开始使用这个强大的文档分类工具吧！"""
        
        ttk.Label(page1, text=welcome_text, font=UIHelpers.get_default_font(), 
                 wraplength=500, justify=tk.LEFT).pack(pady=50)
        
        # 页面2: 新功能介绍
        page2 = ttk.Frame(notebook)
        notebook.add(page2, text="新功能")
        
        features_text = scrolledtext.ScrolledText(page2, wrap=tk.WORD, width=60, height=15)
        features_text.pack(padx=10, pady=10)
        features_text.insert(tk.END, """新功能介绍：

1. 文件预览功能
   - 双击文件或使用预览按钮查看文件内容
   - 支持多种文档格式的内容预览

2. 批量操作
   - 支持多选文件进行批量分类
   - 批量手动分类和移除功能

3. 搜索和过滤
   - 实时搜索文件名
   - 按分类、状态等条件过滤文件

4. 操作撤销
   - 支持撤销文件移动操作
   - 保护重要文件不被误操作

5. 统计分析
   - 生成详细的分类统计报告
   - 分析分类准确率和处理效率

6. 配置管理
   - 支持配置的导入导出
   - 自动检测和修复配置问题

7. 性能优化
   - 多线程处理大文件
   - 智能重复文件检测

8. 增强的错误处理
   - 友好的错误提示
   - 自动错误恢复机制""")
        features_text.config(state=tk.DISABLED)
        
        # 页面3: 完成
        page3 = ttk.Frame(notebook)
        notebook.add(page3, text="开始使用")
        
        ttk.Label(page3, text="设置完成！\n\n点击'开始使用'来体验新版本的强大功能。\n\n"
                             "如需帮助，请使用菜单栏中的'帮助'功能。",
                 font=UIHelpers.get_default_font()).pack(pady=50)
        
        # 完成按钮
        ttk.Button(wizard, text="开始使用", command=wizard.destroy).pack(pady=10)

    def browse_directory(self):
        """浏览选择工作目录（兼容性保留）"""
        self.browse_source_directory()

    def browse_source_directory(self):
        """浏览选择待分类目录"""
        try:
            directory = filedialog.askdirectory(title="选择待分类目录", initialdir=self.work_dir)
            if directory:
                self.work_dir = directory
                self.source_dir_var.set(directory)
                self.refresh_file_list()
                self.populate_directory_tree()  # 刷新目录树
                self.logger.info(f"待分类目录已更改为: {directory}")
        except Exception as e:
            error_msg = f"选择待分类目录失败: {str(e)}"
            self.logger.error(error_msg)
            UIHelpers.show_error("错误", error_msg, self.root)

    def browse_target_directory(self):
        """浏览选择已分类目录"""
        try:
            directory = filedialog.askdirectory(title="选择已分类目录", initialdir=self.target_dir)
            if directory:
                self.target_dir = directory
                self.target_dir_var.set(directory)
                self.logger.info(f"已分类目录已更改为: {directory}")
        except Exception as e:
            error_msg = f"选择已分类目录失败: {str(e)}"
            self.logger.error(error_msg)
            UIHelpers.show_error("错误", error_msg, self.root)

    def refresh_category_list(self):
        """刷新分类规则列表"""
        # 清空现有项
        for item in self.category_tree.get_children():
            self.category_tree.delete(item)

        # 添加分类规则
        categories = self.config_manager.get_categories()
        for config in sorted(categories, key=lambda x: x.get('priority', 0), reverse=True):
            keyword_count = len(config.get('keywords', []))
            self.category_tree.insert('', tk.END, values=(
                config['name'],
                config['priority'],
                keyword_count
            ))

    def refresh_file_list(self):
        """刷新文件列表（异步优化版本）"""
        if hasattr(self, '_scanning') and self._scanning:
            return  # 防止重复扫描

        self._scanning = True

        # 清空现有项
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        self.status_var.set(f"正在扫描目录: {self.work_dir}")
        self.progress_var.set(0)

        # 在主线程中获取UI变量的值
        txt_enabled = self.txt_var.get()
        doc_enabled = self.doc_var.get()
        pdf_enabled = self.pdf_var.get()

        # 在后台线程中扫描文件
        def scan_thread():
            try:
                # 获取选中的文件格式
                allowed_extensions = []
                if txt_enabled:
                    allowed_extensions.append('.txt')
                if doc_enabled:
                    allowed_extensions.extend(['.doc', '.docx'])
                if pdf_enabled:
                    allowed_extensions.append('.pdf')

                # 异步获取文件列表
                self.file_list = self.file_processor.get_files_in_directory(
                    self.work_dir, allowed_extensions
                )

                # 在主线程中更新UI
                self.root.after(0, self._update_file_list_ui)

            except Exception as e:
                self.root.after(0, lambda: self._handle_scan_error(str(e)))

        threading.Thread(target=scan_thread, daemon=True).start()

    def _update_file_list_ui(self):
        """更新文件列表UI（主线程）"""
        try:
            # 分批显示文件，避免UI冻结
            self._display_files_progressively(self.file_list)

            # 更新状态
            self.file_count_var.set(f"文件: {len(self.file_list)}")
            self.status_var.set(f"就绪 - 共发现 {len(self.file_list)} 个可分类文件")
            self.progress_var.set(100)
            logging.info(f"已刷新文件列表，发现 {len(self.file_list)} 个可分类文件")

        finally:
            self._scanning = False

    def _handle_scan_error(self, error_msg):
        """处理扫描错误"""
        self.status_var.set("扫描目录时出错")
        self.progress_var.set(0)
        self._scanning = False
        UIHelpers.show_error("错误", f"扫描目录时出错: {error_msg}", self.root)

    def display_file_list(self, files):
        """显示文件列表"""
        for file_info in files:
            self.file_tree.insert('', tk.END, values=(
                file_info['name'],
                file_info['size_str'],
                file_info['category'],
                file_info['status']
            ))

    def _display_files_progressively(self, files, batch_size=BATCH_DISPLAY_SIZE):
        """分批显示文件，避免UI冻结"""
        def display_batch(start_index):
            end_index = min(start_index + batch_size, len(files))

            # 显示当前批次的文件
            for i in range(start_index, end_index):
                file_info = files[i]
                self.file_tree.insert('', tk.END, values=(
                    file_info['name'],
                    file_info['size_str'],
                    file_info['category'],
                    file_info['status']
                ))

            # 更新进度
            progress = (end_index / len(files)) * 100
            self.progress_var.set(progress)
            self.status_var.set(f"正在加载文件列表... {end_index}/{len(files)}")

            # 如果还有更多文件，继续下一批
            if end_index < len(files):
                self.root.after(10, lambda: display_batch(end_index))

        if files:
            display_batch(0)

    def filter_files(self, _event=None):
        """过滤文件列表"""
        search_term = self.search_var.get().lower()

        # 清空现有显示
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # 过滤并显示文件
        if search_term:
            filtered_files = [
                f for f in self.file_list
                if search_term in f['name'].lower() or search_term in f['category'].lower()
            ]
        else:
            filtered_files = self.file_list

        self.display_file_list(filtered_files)
        self.file_count_var.set(f"文件: {len(filtered_files)}/{len(self.file_list)}")

    def clear_search(self):
        """清除搜索"""
        self.search_var.set("")
        self.filter_files()

    def add_category(self):
        """添加新分类"""
        try:
            existing_names = [c['name'] for c in self.config_manager.get_categories()]
            dialog = CategoryConfigDialog(self.root, existing_names=existing_names)
            self.root.wait_window(dialog.dialog)

            result = dialog.get_result()
            if result:
                self.config_manager.add_category(
                    result['name'],
                    result['keywords'],
                    result['match_type'],
                    result['priority']
                )
                self.refresh_category_list()
                self.refresh_file_list()  # 重新预测分类
                UIHelpers.show_info("成功", f"已添加分类: {result['name']}", self.root)

        except Exception as e:
            UIHelpers.show_error("错误", f"添加分类失败: {str(e)}", self.root)

    def edit_category(self, _event=None):
        """编辑选中的分类"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            UIHelpers.show_warning("警告", "请先选择一个分类", self.root)
            return

        try:
            category_name = self.category_tree.item(selected_item[0], 'values')[0]
            category_data = self.config_manager.get_category_by_name(category_name)

            if not category_data:
                UIHelpers.show_error("错误", "找不到选中的分类", self.root)
                return

            existing_names = [c['name'] for c in self.config_manager.get_categories()]
            dialog = CategoryConfigDialog(self.root, category_data, existing_names)
            self.root.wait_window(dialog.dialog)

            result = dialog.get_result()
            if result:
                self.config_manager.update_category(
                    category_name,
                    result['name'],
                    result['keywords'],
                    result['match_type'],
                    result['priority']
                )
                self.refresh_category_list()
                self.refresh_file_list()  # 重新预测分类
                UIHelpers.show_info("成功", f"已更新分类: {result['name']}", self.root)

        except Exception as e:
            UIHelpers.show_error("错误", f"编辑分类失败: {str(e)}", self.root)

    def delete_category(self):
        """删除选中的分类"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            UIHelpers.show_warning("警告", "请先选择一个分类", self.root)
            return

        try:
            category_name = self.category_tree.item(selected_item[0], 'values')[0]

            if UIHelpers.ask_yes_no("确认", f"确定要删除分类 '{category_name}' 吗?", self.root):
                self.config_manager.delete_category(category_name)
                self.refresh_category_list()
                self.refresh_file_list()  # 重新预测分类
                UIHelpers.show_info("成功", f"已删除分类: {category_name}", self.root)

        except Exception as e:
            UIHelpers.show_error("错误", f"删除分类失败: {str(e)}", self.root)

    def increase_priority(self):
        """提高选中分类的优先级"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            UIHelpers.show_warning("警告", "请先选择一个分类", self.root)
            return

        try:
            category_name = self.category_tree.item(selected_item[0], 'values')[0]
            self.config_manager.adjust_category_priority(category_name, 1)
            self.refresh_category_list()

        except Exception as e:
            UIHelpers.show_error("错误", f"调整优先级失败: {str(e)}", self.root)

    def decrease_priority(self):
        """降低选中分类的优先级"""
        selected_item = self.category_tree.selection()
        if not selected_item:
            UIHelpers.show_warning("警告", "请先选择一个分类", self.root)
            return

        try:
            category_name = self.category_tree.item(selected_item[0], 'values')[0]
            self.config_manager.adjust_category_priority(category_name, -1)
            self.refresh_category_list()

        except Exception as e:
            UIHelpers.show_error("错误", f"调整优先级失败: {str(e)}", self.root)

    def start_processing(self):
        """开始文件分类处理"""
        if self.processing:
            UIHelpers.show_warning("警告", "正在处理中，请稍候", self.root)
            return

        if not self.file_list:
            UIHelpers.show_warning("警告", "没有可处理的文件", self.root)
            return

        # 询问操作模式
        copy_mode = UIHelpers.ask_yes_no(
            "选择操作模式",
            "选择'是'复制文件（保留原文件），选择'否'移动文件（删除原文件）",
            self.root
        )

        # 在后台线程中处理文件
        self.processing = True
        self.status_var.set("正在处理文件...")

        def process_thread():
            try:
                # 创建进度对话框
                progress_dialog = ProgressDialog(self.root, "正在分类文件...")

                def progress_callback(current, total, filename):
                    if not progress_dialog.is_cancelled():
                        progress_dialog.update_progress(current, total, f"正在处理: {filename}")
                        self.progress_var.set((current / total) * 100)
                        self.status_var.set(f"正在处理: {filename} ({current}/{total})")
                    else:
                        self.file_processor.cancel_processing()

                # 获取最优工作线程数
                optimal_workers = self.file_processor.get_optimal_worker_count(len(self.file_list))

                # 估算处理时间
                estimated_time = self.file_processor.estimate_processing_time(self.file_list)
                self.logger.info(f"开始处理 {len(self.file_list)} 个文件，预计耗时 {estimated_time:.1f} 秒，使用 {optimal_workers} 个工作线程")

                # 确保目标目录存在
                os.makedirs(self.target_dir, exist_ok=True)

                # 处理文件
                self.processed_files = self.file_processor.process_files_batch(
                    self.file_list, self.target_dir, copy_mode,
                    max_workers=optimal_workers, progress_callback=progress_callback
                )

                progress_dialog.close()

                # 更新界面
                self.root.after(0, self._processing_completed)

            except Exception as e:
                logging.error(f"文件处理失败: {e}")
                self.root.after(0, lambda: self._processing_failed(str(e)))

        threading.Thread(target=process_thread, daemon=True).start()

    def _processing_completed(self):
        """处理完成后的界面更新"""
        self.processing = False

        # 统计结果
        successful = len([f for f in self.processed_files if f.get('status') == '已完成'])
        failed = len(self.processed_files) - successful

        # 更新状态
        self.status_var.set(f"处理完成 - 成功: {successful}, 失败: {failed}")
        self.progress_var.set(100)

        # 刷新文件列表
        self.refresh_file_list()

        # 播放完成提示音
        try:
            if platform.system() == 'Windows':
                winsound.MessageBeep(winsound.MB_OK)
        except:
            pass

        # 显示结果
        result_msg = f"文件分类完成！\n\n成功处理: {successful} 个文件\n处理失败: {failed} 个文件"
        if failed > 0:
            result_msg += "\n\n查看日志了解失败原因"

        UIHelpers.show_info("处理完成", result_msg, self.root)

        # 询问是否生成汇总文档
        if successful > 0:
            if UIHelpers.ask_yes_no("生成汇总", "是否生成汇总文档？", self.root):
                self.generate_summary()

    def _processing_failed(self, error_msg):
        """处理失败后的界面更新"""
        self.processing = False
        self.status_var.set("处理失败")
        self.progress_var.set(0)
        UIHelpers.show_error("处理失败", f"文件处理过程中出现错误:\n{error_msg}", self.root)

    def generate_summary(self):
        """生成汇总文档"""
        if not self.processed_files:
            UIHelpers.show_warning("警告", "没有已处理的文件可以生成汇总", self.root)
            return

        try:
            self.status_var.set("正在生成汇总文档...")

            # 按分类整理文件内容
            category_files = {}
            total_files = 0
            for file_info in self.processed_files:
                if file_info.get('status') == '已完成' and file_info.get('content'):
                    category = file_info['category']
                    if category not in category_files:
                        category_files[category] = []

                    # 使用文件标题，如果没有则使用文件名
                    title = file_info.get('title', file_info.get('name', '未知文件'))
                    content = file_info['content']

                    category_files[category].append((title, content))
                    total_files += 1

            if not category_files:
                UIHelpers.show_warning("警告", "没有可用的文件内容生成汇总", self.root)
                return

            self.logger.info(f"开始生成汇总文档，共 {total_files} 个文件")

            # 生成汇总文档（包含去重处理）
            output_path = self.document_generator.generate_summary_document(category_files)

            self.status_var.set("汇总文档生成完成")

            # 显示生成结果信息
            result_msg = f"汇总文档已生成: {output_path}\n\n"
            result_msg += f"原始文件数: {total_files}\n"
            result_msg += "已自动去除重复内容\n\n是否立即打开？"

            # 询问是否打开文档
            if UIHelpers.ask_yes_no("生成完成", result_msg, self.root):
                os.startfile(output_path)

        except Exception as e:
            self.status_var.set("生成汇总文档失败")
            UIHelpers.show_error("错误", f"生成汇总文档失败: {str(e)}", self.root)

    def generate_statistics(self):
        """生成统计报告"""
        if not self.processed_files:
            UIHelpers.show_warning("警告", "没有已处理的文件可以生成统计报告", self.root)
            return

        try:
            self.status_var.set("正在生成统计报告...")

            output_path = self.document_generator.generate_statistics_report(self.processed_files)

            self.status_var.set("统计报告生成完成")

            # 询问是否打开报告
            if UIHelpers.ask_yes_no("生成完成", f"统计报告已生成: {output_path}\n\n是否立即打开？", self.root):
                os.startfile(output_path)

        except Exception as e:
            self.status_var.set("生成统计报告失败")
            UIHelpers.show_error("错误", f"生成统计报告失败: {str(e)}", self.root)

    def find_duplicates(self):
        """查找重复文件"""
        if not self.file_list:
            UIHelpers.show_warning("警告", "没有文件可以检查", self.root)
            return

        try:
            self.status_var.set("正在查找重复文件...")

            duplicates = self.file_processor.find_duplicate_files(self.file_list)

            if not duplicates:
                UIHelpers.show_info("查找完成", "没有发现重复文件", self.root)
                self.status_var.set("就绪")
                return

            # 显示重复文件
            self._show_duplicates_dialog(duplicates)
            self.status_var.set("就绪")

        except Exception as e:
            self.status_var.set("查找重复文件失败")
            UIHelpers.show_error("错误", f"查找重复文件失败: {str(e)}", self.root)

    def _show_duplicates_dialog(self, duplicates):
        """显示重复文件对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("重复文件")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        UIHelpers.center_window(dialog, 600, 400)

        # 创建树状视图显示重复文件
        frame = ttk.Frame(dialog, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text=f"发现 {len(duplicates)} 组重复文件:").pack(anchor=tk.W, pady=(0, 10))

        tree = ttk.Treeview(frame, columns=('name', 'size', 'path'), show='headings')
        tree.heading('name', text='文件名')
        tree.heading('size', text='大小')
        tree.heading('path', text='路径')

        tree.column('name', width=200)
        tree.column('size', width=100)
        tree.column('path', width=250)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充数据
        for hash_value, files in duplicates.items():
            parent = tree.insert('', tk.END, text=f"重复组 (哈希: {hash_value[:8]}...)", open=True)
            for file_info in files:
                tree.insert(parent, tk.END, values=(
                    file_info['name'],
                    file_info['size_str'],
                    file_info['path']
                ))

        # 按钮
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(fill=tk.X, pady=10)

        ttk.Button(btn_frame, text="关闭", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def preview_selected_file(self):
        """预览选中的文件"""
        selected_item = self.file_tree.selection()
        if not selected_item:
            UIHelpers.show_warning("警告", "请先选择一个文件", self.root)
            return

        try:
            file_name = self.file_tree.item(selected_item[0], 'values')[0]
            file_info = next((f for f in self.file_list if f['name'] == file_name), None)

            if not file_info:
                UIHelpers.show_error("错误", "找不到选中的文件", self.root)
                return

            # 提取文件内容
            _title, content = self.file_processor.extract_file_content(file_info['path'])

            # 显示预览对话框
            FilePreviewDialog(self.root, file_info['path'], content)

        except Exception as e:
            UIHelpers.show_error("错误", f"预览文件失败: {str(e)}", self.root)

    def open_selected_file(self):
        """打开选中的文件"""
        selected_item = self.file_tree.selection()
        if not selected_item:
            UIHelpers.show_warning("警告", "请先选择一个文件", self.root)
            return

        try:
            file_name = self.file_tree.item(selected_item[0], 'values')[0]
            file_info = next((f for f in self.file_list if f['name'] == file_name), None)

            if not file_info:
                UIHelpers.show_error("错误", "找不到选中的文件", self.root)
                return

            os.startfile(file_info['path'])

        except Exception as e:
            UIHelpers.show_error("错误", f"打开文件失败: {str(e)}", self.root)

    def view_file_properties(self):
        """查看文件属性"""
        selected_item = self.file_tree.selection()
        if not selected_item:
            UIHelpers.show_warning("警告", "请先选择一个文件", self.root)
            return

        try:
            file_name = self.file_tree.item(selected_item[0], 'values')[0]
            file_info = next((f for f in self.file_list if f['name'] == file_name), None)

            if not file_info:
                UIHelpers.show_error("错误", "找不到选中的文件", self.root)
                return

            # 获取文件详细信息
            stat = os.stat(file_info['path'])
            created_time = datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S')
            modified_time = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')

            properties = f"""文件属性:

文件名: {file_info['name']}
路径: {file_info['path']}
大小: {file_info['size_str']} ({file_info['size']} 字节)
预测分类: {file_info['category']}
创建时间: {created_time}
修改时间: {modified_time}
文件哈希: {self.file_processor.get_file_hash(file_info['path'])}"""

            UIHelpers.show_info("文件属性", properties, self.root)

        except Exception as e:
            UIHelpers.show_error("错误", f"获取文件属性失败: {str(e)}", self.root)

    def manual_classify(self):
        """手动分类选中的文件"""
        selected_items = self.file_tree.selection()
        if not selected_items:
            UIHelpers.show_warning("警告", "请先选择要分类的文件", self.root)
            return

        # 获取可用分类
        categories = [c['name'] for c in self.config_manager.get_categories()]

        # 创建分类选择对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("手动分类")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        UIHelpers.center_window(dialog, 300, 200)

        frame = ttk.Frame(dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text=f"为 {len(selected_items)} 个文件选择分类:").pack(pady=(0, 10))

        category_var = tk.StringVar()
        category_combo = ttk.Combobox(frame, textvariable=category_var, values=categories, state='readonly')
        category_combo.pack(fill=tk.X, pady=(0, 20))
        category_combo.current(0)

        def apply_classification():
            selected_category = category_var.get()
            if not selected_category:
                UIHelpers.show_warning("警告", "请选择一个分类", dialog)
                return

            # 更新选中文件的分类
            for item in selected_items:
                file_name = self.file_tree.item(item, 'values')[0]
                file_info = next((f for f in self.file_list if f['name'] == file_name), None)
                if file_info:
                    file_info['category'] = selected_category
                    # 更新显示
                    values = list(self.file_tree.item(item, 'values'))
                    values[2] = selected_category
                    self.file_tree.item(item, values=values)

            dialog.destroy()
            UIHelpers.show_info("成功", f"已将 {len(selected_items)} 个文件分类为: {selected_category}", self.root)

        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X)

        ttk.Button(btn_frame, text="应用", command=apply_classification).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def remove_from_list(self):
        """从列表中移除选中的文件"""
        selected_items = self.file_tree.selection()
        if not selected_items:
            UIHelpers.show_warning("警告", "请先选择要移除的文件", self.root)
            return

        if UIHelpers.ask_yes_no("确认", f"确定要从列表中移除 {len(selected_items)} 个文件吗？", self.root):
            # 获取要移除的文件名
            files_to_remove = []
            for item in selected_items:
                file_name = self.file_tree.item(item, 'values')[0]
                files_to_remove.append(file_name)

            # 从文件列表中移除
            self.file_list = [f for f in self.file_list if f['name'] not in files_to_remove]

            # 刷新显示
            self.display_file_list(self.file_list)
            self.file_count_var.set(f"文件: {len(self.file_list)}")

            UIHelpers.show_info("成功", f"已从列表中移除 {len(files_to_remove)} 个文件", self.root)

    def show_file_context_menu(self, event):
        """显示文件右键菜单"""
        # 选中右键点击的项
        item = self.file_tree.identify_row(event.y)
        if item:
            self.file_tree.selection_set(item)
            self.file_context_menu.post(event.x_root, event.y_root)

    def select_all_files(self):
        """全选文件"""
        for item in self.file_tree.get_children():
            self.file_tree.selection_add(item)

    def undo_operation(self):
        """撤销上一次操作"""
        if not self.operation_history:
            UIHelpers.show_info("提示", "没有可撤销的操作", self.root)
            return

        try:
            last_operation = self.operation_history.pop()

            # 根据操作类型执行撤销
            if last_operation['type'] == 'move':
                # 撤销移动操作：将文件移回原位置
                for file_info in last_operation['files']:
                    if os.path.exists(file_info['dest_path']):
                        shutil.move(file_info['dest_path'], file_info['original_path'])
                        logging.info(f"撤销移动: {file_info['dest_path']} -> {file_info['original_path']}")

            elif last_operation['type'] == 'copy':
                # 撤销复制操作：删除复制的文件
                for file_info in last_operation['files']:
                    if os.path.exists(file_info['dest_path']):
                        os.remove(file_info['dest_path'])
                        logging.info(f"撤销复制: 删除 {file_info['dest_path']}")

            self.refresh_file_list()
            UIHelpers.show_info("成功", f"已撤销 {last_operation['type']} 操作", self.root)

        except Exception as e:
            UIHelpers.show_error("错误", f"撤销操作失败: {str(e)}", self.root)

    def import_config(self):
        """导入配置文件"""
        file_path = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.config_manager.import_config(file_path)
                self.refresh_category_list()
                self.refresh_file_list()
                UIHelpers.show_info("成功", "配置文件导入成功", self.root)

            except Exception as e:
                UIHelpers.show_error("错误", f"导入配置文件失败: {str(e)}", self.root)

    def export_config(self):
        """导出配置文件"""
        file_path = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.config_manager.export_config(file_path)
                UIHelpers.show_info("成功", f"配置文件已导出到: {file_path}", self.root)

            except Exception as e:
                UIHelpers.show_error("错误", f"导出配置文件失败: {str(e)}", self.root)

    def show_help(self):
        """显示帮助信息"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("700x500")
        help_window.transient(self.root)

        UIHelpers.center_window(help_window, 700, 500)

        # 创建带滚动条的文本框
        frame = ttk.Frame(help_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)

        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True)

        help_text = """文件自动分类工具 v2.0 使用说明

=== 基本功能 ===

1. 选择工作目录
   - 点击"选择目录"按钮或使用 Ctrl+O 快捷键
   - 选择包含待分类文件的目录

2. 文件格式选择
   - 勾选要处理的文件格式：.txt、.doc/.docx、.pdf
   - 取消勾选可排除特定格式的文件

3. 开始分类
   - 点击"开始分类"按钮或使用 Ctrl+S 快捷键
   - 选择复制模式（保留原文件）或移动模式（删除原文件）

4. 生成汇总
   - 点击"生成汇总"按钮或使用 Ctrl+G 快捷键
   - 自动生成包含所有分类文件内容的Word文档

=== 高级功能 ===

1. 分类规则管理
   - 在左侧面板中添加、编辑、删除分类规则
   - 调整分类优先级（优先级高的规则优先匹配）
   - 支持三种匹配方式：包含关键词、以关键词开头、默认分类

2. 文件预览和操作
   - 双击文件或点击"预览"按钮查看文件内容
   - 右键点击文件显示上下文菜单
   - 支持手动分类和批量操作

3. 搜索和过滤
   - 使用搜索框快速查找文件
   - 支持按文件名和分类进行过滤

4. 统计分析
   - 生成详细的分类统计报告
   - 查看处理成功率和分类分布

5. 重复文件检测
   - 自动检测并显示重复文件
   - 基于文件内容哈希值进行比较

=== 快捷键 ===

Ctrl+O    选择工作目录
Ctrl+S    开始分类
Ctrl+G    生成汇总
Ctrl+Z    撤销操作
Ctrl+A    全选文件
F5        刷新文件列表
Ctrl+Q    退出程序
Esc       退出程序

=== 配置管理 ===

1. 导入/导出配置
   - 支持配置文件的导入和导出
   - 便于在不同环境间共享分类规则

2. 自动配置修复
   - 自动检测和修复配置文件中的问题
   - 如重复分类、无效规则等

=== 注意事项 ===

1. 建议在处理重要文件前先使用复制模式进行测试
2. 大文件处理可能需要较长时间，请耐心等待
3. 定期备份重要的配置文件和处理结果
4. 如遇到问题，请查看日志文件了解详细信息

=== 技术支持 ===

如有问题或建议，请联系开发团队。
版本: 2.0
更新日期: 2024年"""

        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        ttk.Button(help_window, text="关闭", command=help_window.destroy).pack(pady=10)

    def show_about(self):
        """显示关于信息"""
        about_text = """文件自动分类工具 v2.0

基于关键词的智能文档分类系统

主要特性:
• 支持多种文档格式 (.txt, .doc, .docx, .pdf)
• 智能关键词匹配和分类
• 自动生成Word汇总文档
• 可视化分类规则管理
• 批量文件处理
• 操作撤销功能
• 重复文件检测
• 统计分析报告

技术架构:
• Python + Tkinter GUI
• 模块化设计
• 多线程处理
• 异常处理机制

开发团队: AI Assistant
版本: 2.0
更新日期: 2024年

感谢使用本工具！"""

        UIHelpers.show_info("关于", about_text, self.root)


def setup_logging():
    """设置日志记录"""
    return LoggerConfig.setup_logging(
        log_level=logging.INFO,
        log_file='file_sorter_gui.log'
    )


def main():
    """主函数"""
    # 设置日志
    setup_logging()

    try:
        # 创建主窗口
        root = tk.Tk()

        # 设置窗口图标（如果有的话）
        try:
            root.iconbitmap('icon.ico')
        except:
            pass

        # 创建应用实例
        _app = FileSorterApp(root)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        logging.error(f"应用启动失败: {e}")
        messagebox.showerror("启动错误", f"应用启动失败:\n{str(e)}")


if __name__ == "__main__":
    main()

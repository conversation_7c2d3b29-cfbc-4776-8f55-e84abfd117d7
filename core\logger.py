"""
日志配置模块
提供统一的日志记录配置
"""

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler


class LoggerConfig:
    """日志配置管理器"""
    
    @staticmethod
    def setup_logging(log_level=logging.INFO, log_file='file_sorter.log', 
                     max_bytes=10*1024*1024, backup_count=5):
        """设置日志配置"""
        
        # 创建日志目录
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        log_path = os.path.join(log_dir, log_file)
        
        # 创建根日志记录器
        logger = logging.getLogger()
        logger.setLevel(log_level)
        
        # 清除现有的处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 文件处理器（带轮转）
        file_handler = RotatingFileHandler(
            log_path, 
            maxBytes=max_bytes, 
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)  # 控制台只显示警告和错误
        console_formatter = logging.Formatter('%(levelname)s: %(message)s')
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # 记录启动信息
        logger.info("="*50)
        logger.info("文件自动分类工具启动")
        logger.info(f"日志级别: {logging.getLevelName(log_level)}")
        logger.info(f"日志文件: {log_path}")
        logger.info("="*50)
        
        return logger
    
    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """获取指定名称的日志记录器"""
        return logging.getLogger(name)
    
    @staticmethod
    def log_exception(logger: logging.Logger, exception: Exception, context: str = ""):
        """记录异常信息"""
        if context:
            logger.error(f"{context}: {type(exception).__name__}: {str(exception)}")
        else:
            logger.error(f"{type(exception).__name__}: {str(exception)}")
        logger.debug("异常详情:", exc_info=True)
    
    @staticmethod
    def log_performance(logger: logging.Logger, operation: str, duration: float, 
                       file_count: int = None):
        """记录性能信息"""
        if file_count:
            logger.info(f"性能统计 - {operation}: {duration:.2f}秒, 处理文件: {file_count}个, "
                       f"平均: {duration/file_count:.2f}秒/文件")
        else:
            logger.info(f"性能统计 - {operation}: {duration:.2f}秒")
    
    @staticmethod
    def create_operation_log(operation_type: str, details: dict) -> str:
        """创建操作日志条目"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {operation_type}: {details}"
        return log_entry


# 全局日志记录器实例
_main_logger = None


def get_main_logger() -> logging.Logger:
    """获取主日志记录器"""
    global _main_logger
    if _main_logger is None:
        _main_logger = LoggerConfig.setup_logging()
    return _main_logger


def setup_module_logger(module_name: str) -> logging.Logger:
    """为模块设置专用日志记录器"""
    logger = logging.getLogger(module_name)
    if not logger.handlers:
        # 如果模块日志记录器没有处理器，使用主日志记录器的配置
        main_logger = get_main_logger()
        for handler in main_logger.handlers:
            logger.addHandler(handler)
        logger.setLevel(main_logger.level)
    return logger
